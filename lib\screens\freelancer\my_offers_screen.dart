import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../utils/app_localizations.dart';
import '../../models/offer_model.dart';
import '../../services/offer_service.dart';
import '../chat/chat_screen.dart';

class StatusInfo {
  final IconData icon;
  final String label;
  final Color color;

  StatusInfo({required this.icon, required this.label, required this.color});
}

class MyOffersScreen extends StatefulWidget {
  const MyOffersScreen({super.key});

  @override
  State<MyOffersScreen> createState() => _MyOffersScreenState();
}

class _MyOffersScreenState extends State<MyOffersScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  List<OfferModel> _offers = [];
  bool _isLoading = true;
  OfferStatus _selectedStatus = OfferStatus.pending;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadOffers();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadOffers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
      if (authProvider.user != null) {
        // Load demo offers first
        OfferService.addDemoOffers();

        // Get offers for current freelancer
        final offers = await OfferService.getOffersByFreelancer(authProvider.user!.id);

        if (mounted) {
          setState(() {
            _offers = offers;
            _isLoading = false;
          });
        }
      } else {
        setState(() {
          _offers = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading offers: $e');
      if (mounted) {
        setState(() {
          _offers = [];
          _isLoading = false;
        });
      }
    }
  }

  List<OfferModel> _getOffersByStatus(OfferStatus status) {
    if (status == OfferStatus.rejected) {
      // Include both rejected and withdrawn offers in the rejected tab
      return _offers
          .where((offer) => offer.status == OfferStatus.rejected || offer.status == OfferStatus.withdrawn)
          .toList();
    }
    return _offers.where((offer) => offer.status == status).toList();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final languageProvider = Provider.of<LanguageProvider>(context);

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Column(
        children: [
          // Enhanced Header Section
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.surface,
                  Theme.of(context).colorScheme.surface.withValues(alpha: 0.95),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              boxShadow: [
                BoxShadow(color: Colors.black.withValues(alpha: 0.08), blurRadius: 8, offset: const Offset(0, 2)),
              ],
            ),
            child: Column(
              children: [
                // Enhanced Status Filter Bar
                _buildEnhancedFilterBar(languageProvider),
              ],
            ),
          ),

          // Content
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _buildOffersList(_selectedStatus, l10n, languageProvider),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedFilterBar(LanguageProvider languageProvider) {
    final statuses = [OfferStatus.pending, OfferStatus.accepted, OfferStatus.rejected, OfferStatus.completed];

    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2)),
          boxShadow: [
            BoxShadow(color: Colors.black.withValues(alpha: 0.05), blurRadius: 8, offset: const Offset(0, 2)),
          ],
        ),
        child: Row(
          children:
              statuses.map((status) {
                final isSelected = _selectedStatus == status;
                final count = _getOffersByStatus(status).length;
                final statusInfo = _getStatusInfo(status, languageProvider);

                return Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedStatus = status;
                        _tabController.animateTo(statuses.indexOf(status));
                      });
                    },
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      curve: Curves.easeInOut,
                      margin: const EdgeInsets.symmetric(horizontal: 2),
                      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                      decoration: BoxDecoration(
                        gradient:
                            isSelected
                                ? LinearGradient(
                                  colors: [statusInfo.color, statusInfo.color.withValues(alpha: 0.8)],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                )
                                : null,
                        color: isSelected ? null : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow:
                            isSelected
                                ? [
                                  BoxShadow(
                                    color: statusInfo.color.withValues(alpha: 0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ]
                                : null,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            statusInfo.icon,
                            size: 20,
                            color: isSelected ? Colors.white : statusInfo.color.withValues(alpha: 0.7),
                          ),
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color:
                                  isSelected
                                      ? Colors.white.withValues(alpha: 0.2)
                                      : statusInfo.color.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              count.toString(),
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: isSelected ? Colors.white : statusInfo.color,
                              ),
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            statusInfo.label,
                            style: TextStyle(
                              fontSize: 9,
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                              color:
                                  isSelected
                                      ? Colors.white.withValues(alpha: 0.9)
                                      : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
        ),
      ),
    );
  }

  StatusInfo _getStatusInfo(OfferStatus status, LanguageProvider languageProvider) {
    switch (status) {
      case OfferStatus.pending:
        return StatusInfo(
          icon: Icons.schedule,
          label: languageProvider.isArabic ? 'قيد الانتظار' : 'Pending',
          color: Colors.blue,
        );
      case OfferStatus.accepted:
        return StatusInfo(
          icon: Icons.check_circle,
          label: languageProvider.isArabic ? 'مقبول' : 'Accepted',
          color: Colors.green,
        );
      case OfferStatus.rejected:
        return StatusInfo(
          icon: Icons.cancel,
          label: languageProvider.isArabic ? 'مرفوض' : 'Rejected',
          color: Colors.red,
        );
      case OfferStatus.withdrawn:
        return StatusInfo(
          icon: Icons.remove_circle,
          label: languageProvider.isArabic ? 'مسحوب' : 'Withdrawn',
          color: Colors.grey,
        );
      case OfferStatus.completed:
        return StatusInfo(
          icon: Icons.task_alt,
          label: languageProvider.isArabic ? 'مكتمل' : 'Completed',
          color: Colors.purple,
        );
    }
  }

  Widget _buildOffersList(OfferStatus status, AppLocalizations l10n, LanguageProvider languageProvider) {
    final offers = _getOffersByStatus(status);

    if (offers.isEmpty) {
      return _buildEmptyState(status);
    }

    return RefreshIndicator(
      onRefresh: () async {
        // Simulate API refresh with loading delay
        setState(() {
          _isLoading = true;
        });

        // Simulate network delay
        await Future.delayed(const Duration(milliseconds: 1500));

        // Reload offers (in real app, this would fetch from API)
        _loadOffers();

        // Show refresh success message
        if (mounted) {
          final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.refresh, color: Colors.white, size: 20),
                  const SizedBox(width: 8),
                  Text(languageProvider.isArabic ? 'تم تحديث العروض' : 'Offers refreshed'),
                ],
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 1),
            ),
          );
        }
      },
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        itemCount: offers.length,
        itemBuilder: (context, index) {
          final offer = offers[index];
          return _buildOfferCard(offer, l10n, languageProvider);
        },
      ),
    );
  }

  Widget _buildEmptyState(OfferStatus status) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    String message;
    IconData icon;

    switch (status) {
      case OfferStatus.pending:
        message = languageProvider.isArabic ? 'لا توجد عروض معلقة' : 'No pending offers';
        icon = Icons.pending_outlined;
        break;
      case OfferStatus.accepted:
        message = languageProvider.isArabic ? 'لا توجد عروض مقبولة بعد' : 'No accepted offers yet';
        icon = Icons.check_circle_outline;
        break;
      case OfferStatus.rejected:
        message = languageProvider.isArabic ? 'لا توجد عروض مرفوضة' : 'No rejected offers';
        icon = Icons.cancel_outlined;
        break;
      case OfferStatus.withdrawn:
        message = languageProvider.isArabic ? 'لا توجد عروض مسحوبة' : 'No withdrawn offers';
        icon = Icons.remove_circle_outline;
        break;
      case OfferStatus.completed:
        message = languageProvider.isArabic ? 'لا توجد مشاريع مكتملة بعد' : 'No completed projects yet';
        icon = Icons.task_alt_outlined;
        break;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5)),
          const SizedBox(height: 16),
          Text(message, style: Theme.of(context).textTheme.titleLarge),
          const SizedBox(height: 8),
          Text(
            languageProvider.isArabic
                ? 'ستظهر العروض هنا عندما ترسلها للعملاء'
                : 'Offers will appear here when you send them to clients',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOfferCard(OfferModel offer, AppLocalizations l10n, LanguageProvider languageProvider) {
    // Use different card layout for completed offers
    if (offer.status == OfferStatus.completed) {
      return _buildCompletedOfferCard(offer, l10n, languageProvider);
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 10),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(color: _getStatusBorderColor(offer.status), width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with offer ID and status
            Row(
              children: [
                Expanded(
                  child: Text(
                    languageProvider.isArabic ? 'عرض #${offer.id}' : 'Offer #${offer.id}',
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(offer.status).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatusText(offer.status, languageProvider.isArabic),
                    style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: _getStatusColor(offer.status)),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Request info
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200, width: 1),
              ),
              child: Row(
                children: [
                  Icon(Icons.work, size: 16, color: Colors.blue[700]),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      languageProvider.isArabic ? 'الطلب: ${offer.requestId}' : 'Request: ${offer.requestId}',
                      style: TextStyle(fontWeight: FontWeight.w600, color: Colors.blue[700]),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Offer details
            Row(
              children: [
                Expanded(
                  child: _buildOfferDetail(
                    languageProvider.isArabic ? 'السعر' : 'Price',
                    '\$${offer.price.toStringAsFixed(0)}',
                    Icons.attach_money,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildOfferDetail(
                    languageProvider.isArabic ? 'التسليم' : 'Delivery',
                    languageProvider.isArabic ? '${offer.deliveryDays} أيام' : '${offer.deliveryDays} days',
                    Icons.schedule,
                    Colors.orange,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Description
            Text(
              offer.description,
              style: Theme.of(context).textTheme.bodyMedium,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),

            const SizedBox(height: 12),

            // Timestamps
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  languageProvider.isArabic
                      ? 'تم الإرسال ${languageProvider.getTimeAgo(offer.createdAt)}'
                      : 'Sent ${languageProvider.getTimeAgo(offer.createdAt)}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
                if (offer.updatedAt != null) ...[
                  const SizedBox(width: 16),
                  Icon(Icons.update, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    languageProvider.isArabic
                        ? 'تم التحديث ${languageProvider.getTimeAgo(offer.updatedAt!)}'
                        : 'Updated ${languageProvider.getTimeAgo(offer.updatedAt!)}',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ],
            ),

            // Rejection reason (if rejected)
            if (offer.status == OfferStatus.rejected && offer.rejectionReason != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.red[700], size: 16),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            languageProvider.isArabic ? 'سبب الرفض:' : 'Rejection Reason:',
                            style: TextStyle(fontWeight: FontWeight.w600, color: Colors.red[700], fontSize: 12),
                          ),
                          Text(offer.rejectionReason!, style: const TextStyle(fontSize: 12)),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],

            const SizedBox(height: 16),

            // Action buttons
            _buildActionButtons(offer),
          ],
        ),
      ),
    );
  }

  Widget _buildOfferDetail(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(color: color.withValues(alpha: 0.1), borderRadius: BorderRadius.circular(8)),
      child: Row(
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(label, style: TextStyle(fontSize: 10, color: color, fontWeight: FontWeight.w600)),
                Text(value, style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(OfferModel offer) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);

    switch (offer.status) {
      case OfferStatus.pending:
        return Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _editOffer(offer),
                icon: const Icon(Icons.edit),
                label: Text(languageProvider.isArabic ? 'تعديل' : 'Edit'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _withdrawOffer(offer),
                icon: const Icon(Icons.remove_circle),
                label: Text(languageProvider.isArabic ? 'سحب' : 'Withdraw'),
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red, foregroundColor: Colors.white),
              ),
            ),
          ],
        );

      case OfferStatus.accepted:
        return Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _openConversation(offer),
                icon: const Icon(Icons.chat_bubble_outline),
                label: Text(languageProvider.isArabic ? 'محادثة' : 'Chat'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.green.shade700,
                  side: BorderSide(color: Colors.green.shade300),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => _viewJob(offer),
                icon: const Icon(Icons.work),
                label: Text(languageProvider.isArabic ? 'عرض الوظيفة' : 'View Job'),
                style: ElevatedButton.styleFrom(backgroundColor: Colors.green, foregroundColor: Colors.white),
              ),
            ),
          ],
        );

      case OfferStatus.rejected:
        return Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => _viewRejectionDetails(offer),
                icon: const Icon(Icons.info),
                label: Text(languageProvider.isArabic ? 'عرض التفاصيل' : 'View Details'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton(
                onPressed: () => _sendNewOffer(offer),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.refresh, size: 16),
                    const SizedBox(width: 4),
                    Flexible(
                      child: Text(
                        languageProvider.isArabic ? 'إرسال عرض جديد' : 'Send New Offer',
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(fontSize: 13),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );

      case OfferStatus.withdrawn:
        return SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => _sendNewOffer(offer),
            icon: const Icon(Icons.refresh, size: 16),
            label: Text(languageProvider.isArabic ? 'إرسال عرض جديد' : 'Send New Offer'),
          ),
        );

      case OfferStatus.completed:
        return SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () => _viewCompletedProject(offer),
            icon: const Icon(Icons.visibility),
            label: Text(languageProvider.isArabic ? 'عرض المشروع' : 'View Project'),
          ),
        );
    }
  }

  Color _getStatusColor(OfferStatus status) {
    switch (status) {
      case OfferStatus.pending:
        return Colors.orange;
      case OfferStatus.accepted:
        return Colors.green;
      case OfferStatus.rejected:
        return Colors.red;
      case OfferStatus.withdrawn:
        return Colors.grey;
      case OfferStatus.completed:
        return Colors.purple;
    }
  }

  Color _getStatusBorderColor(OfferStatus status) {
    switch (status) {
      case OfferStatus.pending:
        return Colors.orange.shade200;
      case OfferStatus.accepted:
        return Colors.green.shade200;
      case OfferStatus.rejected:
        return Colors.red.shade200;
      case OfferStatus.withdrawn:
        return Colors.grey.shade300;
      case OfferStatus.completed:
        return Colors.purple.shade200;
    }
  }

  String _getStatusText(OfferStatus status, bool isArabic) {
    switch (status) {
      case OfferStatus.pending:
        return isArabic ? 'معلق' : 'PENDING';
      case OfferStatus.accepted:
        return isArabic ? 'مقبول' : 'ACCEPTED';
      case OfferStatus.rejected:
        return isArabic ? 'مرفوض' : 'REJECTED';
      case OfferStatus.withdrawn:
        return isArabic ? 'مسحوب' : 'WITHDRAWN';
      case OfferStatus.completed:
        return isArabic ? 'مكتمل' : 'COMPLETED';
    }
  }

  void _editOffer(OfferModel offer) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final priceController = TextEditingController(text: offer.price.toString());
    final deliveryController = TextEditingController(text: offer.deliveryDays.toString());
    final descriptionController = TextEditingController(text: offer.description);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(languageProvider.isArabic ? 'تعديل العرض' : 'Edit Offer'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: priceController,
                    decoration: InputDecoration(
                      labelText: languageProvider.isArabic ? 'السعر (\$)' : 'Price (\$)',
                      border: const OutlineInputBorder(),
                      errorText: _validatePrice(priceController.text, languageProvider.isArabic),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      // Trigger rebuild to show validation
                      (context as Element).markNeedsBuild();
                    },
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: deliveryController,
                    decoration: InputDecoration(
                      labelText: languageProvider.isArabic ? 'أيام التسليم' : 'Delivery Days',
                      border: const OutlineInputBorder(),
                      errorText: _validateDeliveryDays(deliveryController.text, languageProvider.isArabic),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      // Trigger rebuild to show validation
                      (context as Element).markNeedsBuild();
                    },
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: descriptionController,
                    decoration: InputDecoration(
                      labelText: languageProvider.isArabic ? 'الوصف' : 'Description',
                      border: const OutlineInputBorder(),
                      errorText: _validateDescription(descriptionController.text, languageProvider.isArabic),
                    ),
                    maxLines: 3,
                    onChanged: (value) {
                      // Trigger rebuild to show validation
                      (context as Element).markNeedsBuild();
                    },
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(languageProvider.isArabic ? 'إلغاء' : 'Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  // Validate all fields before saving
                  final priceError = _validatePrice(priceController.text, languageProvider.isArabic);
                  final deliveryError = _validateDeliveryDays(deliveryController.text, languageProvider.isArabic);
                  final descriptionError = _validateDescription(descriptionController.text, languageProvider.isArabic);

                  if (priceError != null || deliveryError != null || descriptionError != null) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          languageProvider.isArabic ? 'يرجى تصحيح الأخطاء أولاً' : 'Please fix the errors first',
                        ),
                        backgroundColor: Colors.red,
                      ),
                    );
                    return;
                  }

                  Navigator.pop(context);
                  setState(() {
                    final index = _offers.indexWhere((o) => o.id == offer.id);
                    if (index != -1) {
                      _offers[index] = offer.copyWith(
                        price: double.parse(priceController.text),
                        deliveryDays: int.parse(deliveryController.text),
                        description: descriptionController.text.trim(),
                        updatedAt: DateTime.now(),
                      );
                    }
                  });
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        languageProvider.isArabic ? 'تم تحديث العرض بنجاح!' : 'Offer updated successfully!',
                      ),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
                child: Text(languageProvider.isArabic ? 'تحديث' : 'Update'),
              ),
            ],
          ),
    );
  }

  String? _validatePrice(String value, bool isArabic) {
    if (value.isEmpty) {
      return isArabic ? 'السعر مطلوب' : 'Price is required';
    }
    final price = double.tryParse(value);
    if (price == null) {
      return isArabic ? 'يرجى إدخال رقم صحيح' : 'Please enter a valid number';
    }
    if (price <= 0) {
      return isArabic ? 'السعر يجب أن يكون أكبر من صفر' : 'Price must be greater than zero';
    }
    if (price > 10000) {
      return isArabic ? 'السعر يجب أن يكون أقل من 10,000' : 'Price must be less than 10,000';
    }
    return null;
  }

  String? _validateDeliveryDays(String value, bool isArabic) {
    if (value.isEmpty) {
      return isArabic ? 'أيام التسليم مطلوبة' : 'Delivery days is required';
    }
    final days = int.tryParse(value);
    if (days == null) {
      return isArabic ? 'يرجى إدخال رقم صحيح' : 'Please enter a valid number';
    }
    if (days <= 0) {
      return isArabic ? 'أيام التسليم يجب أن تكون أكبر من صفر' : 'Delivery days must be greater than zero';
    }
    if (days > 365) {
      return isArabic ? 'أيام التسليم يجب أن تكون أقل من 365' : 'Delivery days must be less than 365';
    }
    return null;
  }

  String? _validateDescription(String value, bool isArabic) {
    if (value.trim().isEmpty) {
      return isArabic ? 'الوصف مطلوب' : 'Description is required';
    }
    if (value.trim().length < 10) {
      return isArabic ? 'الوصف يجب أن يكون 10 أحرف على الأقل' : 'Description must be at least 10 characters';
    }
    if (value.trim().length > 500) {
      return isArabic ? 'الوصف يجب أن يكون أقل من 500 حرف' : 'Description must be less than 500 characters';
    }
    return null;
  }

  void _withdrawOffer(OfferModel offer) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                const Icon(Icons.warning, color: Colors.orange, size: 24),
                const SizedBox(width: 8),
                Text(languageProvider.isArabic ? 'سحب العرض' : 'Withdraw Offer'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  languageProvider.isArabic
                      ? 'هل أنت متأكد من أنك تريد سحب العرض #${offer.id}؟'
                      : 'Are you sure you want to withdraw offer #${offer.id}?',
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info, color: Colors.orange[700], size: 16),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          languageProvider.isArabic
                              ? 'لن تتمكن من التراجع عن هذا الإجراء'
                              : 'This action cannot be undone',
                          style: TextStyle(fontSize: 12, color: Colors.orange[700]),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(languageProvider.isArabic ? 'إلغاء' : 'Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);

                  // Show loading indicator
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Row(
                        children: [
                          const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                          ),
                          const SizedBox(width: 12),
                          Text(languageProvider.isArabic ? 'جاري سحب العرض...' : 'Withdrawing offer...'),
                        ],
                      ),
                      duration: const Duration(seconds: 1),
                    ),
                  );

                  // Store context reference before async operation
                  final scaffoldMessenger = ScaffoldMessenger.of(context);

                  // Simulate API call delay
                  Future.delayed(const Duration(milliseconds: 800), () {
                    if (mounted) {
                      setState(() {
                        final index = _offers.indexWhere((o) => o.id == offer.id);
                        if (index != -1) {
                          _offers[index] = offer.copyWith(status: OfferStatus.withdrawn, updatedAt: DateTime.now());
                        }

                        // Switch to rejected tab if currently on pending tab
                        if (_selectedStatus == OfferStatus.pending) {
                          _selectedStatus = OfferStatus.rejected;
                        }
                      });

                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Row(
                            children: [
                              const Icon(Icons.check_circle, color: Colors.white, size: 20),
                              const SizedBox(width: 8),
                              Text(languageProvider.isArabic ? 'تم سحب العرض بنجاح' : 'Offer withdrawn successfully'),
                            ],
                          ),
                          backgroundColor: Colors.green,
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    }
                  });
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red, foregroundColor: Colors.white),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.remove_circle, size: 18),
                    const SizedBox(width: 4),
                    Text(languageProvider.isArabic ? 'سحب' : 'Withdraw'),
                  ],
                ),
              ),
            ],
          ),
    );
  }

  void _viewJob(OfferModel offer) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);

    // Show job/request details in a dialog
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                const Icon(Icons.work, color: Colors.blue, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    languageProvider.isArabic ? 'تفاصيل الطلب' : 'Request Details',
                    style: const TextStyle(fontSize: 18),
                  ),
                ),
              ],
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Request ID
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.tag, color: Colors.blue[700], size: 16),
                        const SizedBox(width: 8),
                        Text(
                          languageProvider.isArabic
                              ? 'رقم الطلب: ${offer.requestId}'
                              : 'Request ID: ${offer.requestId}',
                          style: TextStyle(fontWeight: FontWeight.w600, color: Colors.blue[700]),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 12),

                  // Offer details
                  Text(
                    languageProvider.isArabic ? 'تفاصيل عرضك:' : 'Your Offer Details:',
                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),

                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    languageProvider.isArabic ? 'السعر' : 'Price',
                                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                                  ),
                                  Text(
                                    '\$${offer.price.toStringAsFixed(0)}',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.green,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    languageProvider.isArabic ? 'التسليم' : 'Delivery',
                                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                                  ),
                                  Text(
                                    languageProvider.isArabic
                                        ? '${offer.deliveryDays} أيام'
                                        : '${offer.deliveryDays} days',
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.orange,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          languageProvider.isArabic ? 'الوصف:' : 'Description:',
                          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                        ),
                        const SizedBox(height: 4),
                        Text(offer.description, style: const TextStyle(fontSize: 14)),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            Navigator.pop(context);
                            _openConversation(offer);
                          },
                          icon: const Icon(Icons.chat_bubble_outline),
                          label: Text(languageProvider.isArabic ? 'محادثة' : 'Chat'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Navigator.pop(context);
                            // Navigate to browse requests and highlight this request
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  languageProvider.isArabic
                                      ? 'الانتقال إلى صفحة تصفح الطلبات...'
                                      : 'Navigating to browse requests...',
                                ),
                              ),
                            );
                          },
                          icon: const Icon(Icons.search),
                          label: Text(languageProvider.isArabic ? 'تصفح' : 'Browse'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(languageProvider.isArabic ? 'إغلاق' : 'Close'),
              ),
            ],
          ),
    );
  }

  void _viewRejectionDetails(OfferModel offer) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                const Icon(Icons.cancel, color: Colors.red, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    languageProvider.isArabic ? 'تفاصيل الرفض' : 'Rejection Details',
                    style: const TextStyle(fontSize: 18),
                  ),
                ),
              ],
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Offer header
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red.shade200),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.work, color: Colors.red[700], size: 20),
                            const SizedBox(width: 8),
                            Text(
                              languageProvider.isArabic ? 'عرض #${offer.id}' : 'Offer #${offer.id}',
                              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.red[700]),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          languageProvider.isArabic ? 'الطلب: ${offer.requestId}' : 'Request: ${offer.requestId}',
                          style: TextStyle(fontSize: 14, color: Colors.red[600]),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Offer details
                  Text(
                    languageProvider.isArabic ? 'تفاصيل العرض المرفوض:' : 'Rejected Offer Details:',
                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),

                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.grey.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Icon(Icons.attach_money, size: 16, color: Colors.green),
                                  const SizedBox(width: 4),
                                  Text(
                                    languageProvider.isArabic ? 'السعر' : 'Price',
                                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '\$${offer.price.toStringAsFixed(0)}',
                                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.grey.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Icon(Icons.schedule, size: 16, color: Colors.orange),
                                  const SizedBox(width: 4),
                                  Text(
                                    languageProvider.isArabic ? 'التسليم' : 'Delivery',
                                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),
                              Text(
                                languageProvider.isArabic ? '${offer.deliveryDays} أيام' : '${offer.deliveryDays} days',
                                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Rejection reason
                  Text(
                    languageProvider.isArabic ? 'سبب الرفض:' : 'Rejection Reason:',
                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),

                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color:
                          offer.rejectionReason != null
                              ? Colors.orange.withValues(alpha: 0.1)
                              : Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: offer.rejectionReason != null ? Colors.orange.shade200 : Colors.grey.shade300,
                      ),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          offer.rejectionReason != null ? Icons.info : Icons.help_outline,
                          color: offer.rejectionReason != null ? Colors.orange[700] : Colors.grey[600],
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            offer.rejectionReason ??
                                (languageProvider.isArabic
                                    ? 'لم يتم تقديم سبب محدد للرفض'
                                    : 'No specific reason provided for rejection'),
                            style: TextStyle(
                              fontSize: 14,
                              height: 1.5,
                              color: offer.rejectionReason != null ? Colors.orange[800] : Colors.grey[700],
                              fontStyle: offer.rejectionReason != null ? FontStyle.normal : FontStyle.italic,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            Navigator.pop(context);
                            _sendNewOffer(offer);
                          },
                          icon: const Icon(Icons.refresh),
                          label: Text(languageProvider.isArabic ? 'عرض جديد' : 'New Offer'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.green,
                            side: BorderSide(color: Colors.green.shade300),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Navigator.pop(context);
                            _viewJob(offer);
                          },
                          icon: const Icon(Icons.work),
                          label: Text(languageProvider.isArabic ? 'عرض الطلب' : 'View Request'),
                          style: ElevatedButton.styleFrom(backgroundColor: Colors.blue, foregroundColor: Colors.white),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(languageProvider.isArabic ? 'إغلاق' : 'Close'),
              ),
            ],
          ),
    );
  }

  void _sendNewOffer(OfferModel offer) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final priceController = TextEditingController(text: offer.price.toString());
    final deliveryController = TextEditingController(text: offer.deliveryDays.toString());
    final descriptionController = TextEditingController(text: offer.description);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                const Icon(Icons.add_circle, color: Colors.green, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    languageProvider.isArabic ? 'إرسال عرض جديد' : 'Send New Offer',
                    style: const TextStyle(fontSize: 18),
                  ),
                ),
              ],
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Info about original offer
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info, color: Colors.blue[700], size: 16),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            languageProvider.isArabic
                                ? 'إرسال عرض جديد للطلب: ${offer.requestId}'
                                : 'Sending new offer for request: ${offer.requestId}',
                            style: TextStyle(fontSize: 12, color: Colors.blue[700]),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Price field
                  TextField(
                    controller: priceController,
                    decoration: InputDecoration(
                      labelText: languageProvider.isArabic ? 'السعر الجديد (\$)' : 'New Price (\$)',
                      border: const OutlineInputBorder(),
                      prefixIcon: const Icon(Icons.attach_money),
                      errorText: _validatePrice(priceController.text, languageProvider.isArabic),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      (context as Element).markNeedsBuild();
                    },
                  ),
                  const SizedBox(height: 16),

                  // Delivery days field
                  TextField(
                    controller: deliveryController,
                    decoration: InputDecoration(
                      labelText: languageProvider.isArabic ? 'أيام التسليم الجديدة' : 'New Delivery Days',
                      border: const OutlineInputBorder(),
                      prefixIcon: const Icon(Icons.schedule),
                      errorText: _validateDeliveryDays(deliveryController.text, languageProvider.isArabic),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      (context as Element).markNeedsBuild();
                    },
                  ),
                  const SizedBox(height: 16),

                  // Description field
                  TextField(
                    controller: descriptionController,
                    decoration: InputDecoration(
                      labelText: languageProvider.isArabic ? 'الوصف المحدث' : 'Updated Description',
                      border: const OutlineInputBorder(),
                      prefixIcon: const Icon(Icons.description),
                      errorText: _validateDescription(descriptionController.text, languageProvider.isArabic),
                    ),
                    maxLines: 3,
                    onChanged: (value) {
                      (context as Element).markNeedsBuild();
                    },
                  ),

                  const SizedBox(height: 12),

                  // Tip
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.lightbulb, color: Colors.green[700], size: 16),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            languageProvider.isArabic
                                ? 'نصيحة: حاول تحسين عرضك بناءً على سبب الرفض السابق'
                                : 'Tip: Try to improve your offer based on the previous rejection reason',
                            style: TextStyle(fontSize: 12, color: Colors.green[700]),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(languageProvider.isArabic ? 'إلغاء' : 'Cancel'),
              ),
              ElevatedButton(
                onPressed: () async {
                  // Validate all fields
                  final priceError = _validatePrice(priceController.text, languageProvider.isArabic);
                  final deliveryError = _validateDeliveryDays(deliveryController.text, languageProvider.isArabic);
                  final descriptionError = _validateDescription(descriptionController.text, languageProvider.isArabic);

                  if (priceError != null || deliveryError != null || descriptionError != null) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          languageProvider.isArabic ? 'يرجى تصحيح الأخطاء أولاً' : 'Please fix the errors first',
                        ),
                        backgroundColor: Colors.red,
                      ),
                    );
                    return;
                  }

                  Navigator.pop(context);

                  // Capture context and language provider before async operation
                  final scaffoldMessenger = ScaffoldMessenger.of(context);
                  final isArabic = languageProvider.isArabic;

                  // Withdraw the offer
                  try {
                    await OfferService.withdrawOffer(offer.id);

                    if (mounted) {
                      setState(() {
                        // Update the offer status in the list
                        final index = _offers.indexWhere((o) => o.id == offer.id);
                        if (index != -1) {
                          _offers[index] = _offers[index].copyWith(
                            status: OfferStatus.withdrawn,
                            withdrawnAt: DateTime.now(),
                          );
                        }
                      });
                    }
                  } catch (e) {
                    print('Error withdrawing offer: $e');
                  }

                  if (mounted) {
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Row(
                          children: [
                            const Icon(Icons.cancel, color: Colors.white, size: 20),
                            const SizedBox(width: 8),
                            Text(isArabic ? 'تم سحب العرض بنجاح' : 'Offer withdrawn successfully'),
                          ],
                        ),
                        backgroundColor: Colors.orange,
                        duration: const Duration(seconds: 3),
                      ),
                    );
                  }
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.green, foregroundColor: Colors.white),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.send, size: 18),
                    const SizedBox(width: 4),
                    Text(languageProvider.isArabic ? 'إرسال' : 'Send'),
                  ],
                ),
              ),
            ],
          ),
    );
  }

  void _viewCompletedProject(OfferModel offer) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                const Icon(Icons.task_alt, color: Colors.purple, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    languageProvider.isArabic ? 'المشروع المكتمل' : 'Completed Project',
                    style: const TextStyle(fontSize: 18),
                  ),
                ),
              ],
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Project header
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.purple.shade50, Colors.purple.shade100],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.purple.shade200),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.work, color: Colors.purple[700], size: 20),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                languageProvider.isArabic ? 'مشروع #${offer.id}' : 'Project #${offer.id}',
                                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.purple[700]),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          languageProvider.isArabic ? 'الطلب: ${offer.requestId}' : 'Request: ${offer.requestId}',
                          style: TextStyle(fontSize: 14, color: Colors.purple[600]),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Project details
                  Row(
                    children: [
                      Expanded(
                        child: _buildProjectDetailCard(
                          languageProvider.isArabic ? 'المبلغ المدفوع' : 'Amount Paid',
                          '\$${offer.price.toStringAsFixed(0)}',
                          Icons.attach_money,
                          Colors.green,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildProjectDetailCard(
                          languageProvider.isArabic ? 'تاريخ التسليم' : 'Delivered',
                          _formatSubmissionDate(offer.submissionDate, languageProvider.isArabic),
                          Icons.calendar_today,
                          Colors.blue,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Project description
                  Text(
                    languageProvider.isArabic ? 'وصف المشروع:' : 'Project Description:',
                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Text(offer.description, style: const TextStyle(fontSize: 14, height: 1.5)),
                  ),

                  const SizedBox(height: 16),

                  // Client feedback section
                  if (offer.clientRating != null || (offer.clientReview != null && offer.clientReview!.isNotEmpty)) ...[
                    Text(
                      languageProvider.isArabic ? 'تقييم العميل:' : 'Client Feedback:',
                      style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.amber.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.amber.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (offer.clientRating != null) ...[
                            Row(
                              children: [
                                ...List.generate(5, (index) {
                                  return Icon(
                                    index < offer.clientRating!.floor()
                                        ? Icons.star
                                        : index < offer.clientRating!
                                        ? Icons.star_half
                                        : Icons.star_border,
                                    color: Colors.amber.shade600,
                                    size: 20,
                                  );
                                }),
                                const SizedBox(width: 8),
                                Text(
                                  offer.clientRating!.toStringAsFixed(1),
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                    color: Colors.amber.shade800,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                          ],
                          if (offer.clientReview != null && offer.clientReview!.isNotEmpty) ...[
                            Text(
                              offer.clientReview!,
                              style: const TextStyle(fontSize: 14, height: 1.5, fontStyle: FontStyle.italic),
                            ),
                          ],
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            Navigator.pop(context);
                            _openConversation(offer);
                          },
                          icon: const Icon(Icons.chat_bubble_outline),
                          label: Text(languageProvider.isArabic ? 'عرض المحادثة' : 'View Chat'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Navigator.pop(context);
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  languageProvider.isArabic ? 'تم نسخ رابط المشروع' : 'Project link copied',
                                ),
                              ),
                            );
                          },
                          icon: const Icon(Icons.share),
                          label: Text(languageProvider.isArabic ? 'مشاركة' : 'Share'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.purple,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(languageProvider.isArabic ? 'إغلاق' : 'Close'),
              ),
            ],
          ),
    );
  }

  Widget _buildProjectDetailCard(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Text(label, style: TextStyle(fontSize: 12, color: color, fontWeight: FontWeight.w600)),
            ],
          ),
          const SizedBox(height: 4),
          Text(value, style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Widget _buildCompletedOfferCard(OfferModel offer, AppLocalizations l10n, LanguageProvider languageProvider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.purple.shade200, width: 1.5),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with project title and completed badge
            Row(
              children: [
                Expanded(
                  child: Text(
                    languageProvider.isArabic ? 'مشروع #${offer.id}' : 'Project #${offer.id}',
                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(colors: [Colors.purple.shade400, Colors.purple.shade600]),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.task_alt, color: Colors.white, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        languageProvider.isArabic ? 'مكتمل' : 'COMPLETED',
                        style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Price and submission date
            Row(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.green.shade200, width: 1),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.attach_money, color: Colors.green.shade700, size: 20),
                        const SizedBox(width: 8),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              languageProvider.isArabic ? 'المبلغ المدفوع' : 'Amount Paid',
                              style: TextStyle(fontSize: 12, color: Colors.green.shade700, fontWeight: FontWeight.w600),
                            ),
                            Text(
                              '\$${offer.price.toStringAsFixed(0)}',
                              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.green.shade800),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200, width: 1),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.calendar_today, color: Colors.blue.shade700, size: 20),
                        const SizedBox(width: 8),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              languageProvider.isArabic ? 'تاريخ التسليم' : 'Submitted',
                              style: TextStyle(fontSize: 12, color: Colors.blue.shade700, fontWeight: FontWeight.w600),
                            ),
                            Text(
                              _formatSubmissionDate(offer.submissionDate, languageProvider.isArabic),
                              style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: Colors.blue.shade800),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Client feedback (rating + review merged)
            if (offer.clientRating != null || (offer.clientReview != null && offer.clientReview!.isNotEmpty)) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.amber.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Colors.amber.shade200, width: 1),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header with rating
                    Row(
                      children: [
                        Icon(Icons.rate_review, color: Colors.amber.shade700, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          languageProvider.isArabic ? 'تقييم وتعليق العميل' : 'Client Feedback',
                          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16, color: Colors.amber.shade800),
                        ),
                        const Spacer(),
                        if (offer.clientRating != null) ...[
                          Row(
                            children: List.generate(5, (index) {
                              return Icon(
                                index < offer.clientRating!.floor()
                                    ? Icons.star
                                    : index < offer.clientRating!
                                    ? Icons.star_half
                                    : Icons.star_border,
                                color: Colors.amber.shade600,
                                size: 18,
                              );
                            }),
                          ),
                          const SizedBox(width: 6),
                          Text(
                            offer.clientRating!.toStringAsFixed(1),
                            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16, color: Colors.amber.shade800),
                          ),
                        ],
                      ],
                    ),

                    // Review text
                    if (offer.clientReview != null && offer.clientReview!.isNotEmpty) ...[
                      const SizedBox(height: 10),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.8),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.amber.shade100, width: 1),
                        ),
                        child: Text(
                          offer.clientReview!,
                          style: TextStyle(
                            color: Colors.grey.shade800,
                            fontSize: 14,
                            height: 1.5,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              const SizedBox(height: 12),
            ],

            // Action button - View conversation
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _openConversation(offer),
                icon: const Icon(Icons.chat_bubble_outline),
                label: Text(languageProvider.isArabic ? 'عرض المحادثة' : 'View Conversation'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: BorderSide(color: Colors.purple.shade400, width: 1),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatSubmissionDate(DateTime? date, bool isArabic) {
    if (date == null) return isArabic ? 'غير محدد' : 'Not specified';

    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return isArabic ? 'اليوم' : 'Today';
    } else if (difference == 1) {
      return isArabic ? 'أمس' : 'Yesterday';
    } else if (difference < 7) {
      return isArabic ? 'منذ $difference أيام' : '$difference days ago';
    } else if (difference < 30) {
      final weeks = (difference / 7).floor();
      return isArabic ? 'منذ $weeks أسبوع' : '$weeks weeks ago';
    } else {
      final months = (difference / 30).floor();
      return isArabic ? 'منذ $months شهر' : '$months months ago';
    }
  }

  void _openConversation(OfferModel offer) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);

    // Navigate to chat screen with offer context
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => ChatScreen(
              chatId: 'offer_${offer.id}',
              recipientName: languageProvider.isArabic ? 'العميل' : 'Client',
              requestTitle:
                  languageProvider.isArabic
                      ? 'عرض #${offer.id} - ${offer.requestId}'
                      : 'Offer #${offer.id} - ${offer.requestId}',
              isAdminChat: false,
              // For accepted offers, we might have an order ID
              orderId: offer.status == OfferStatus.accepted ? 'order_${offer.id}' : null,
            ),
      ),
    );
  }
}
