import '../models/offer_model.dart';
import 'smart_notification_service.dart';

class OfferService {
  /// Submit a new offer
  static Future<OfferModel> submitOffer(OfferModel offer) async {
    try {
      // In production, this would save to database
      // For demo, we'll simulate the API call
      await Future.delayed(const Duration(seconds: 1));

      // Add to demo storage
      _demoOffers.add(offer);

      // Send new offer notification to client
      SmartNotificationService.notifyOfferReceived(
        clientId: 'demo_client_${offer.requestId}', // In real app, get from request
        offerId: offer.id,
        freelancerName: offer.freelancerName,
        offerAmount: offer.price,
        serviceTitle: 'Service Request',
      );

      return offer;
    } catch (e) {
      throw Exception('Failed to submit offer: $e');
    }
  }

  /// Get offers for a specific request
  static Future<List<OfferModel>> getOffersForRequest(String requestId) async {
    try {
      // In production, this would query the database
      await Future.delayed(const Duration(milliseconds: 500));

      return _demoOffers.where((offer) => offer.requestId == requestId).toList();
    } catch (e) {
      throw Exception('Failed to get offers: $e');
    }
  }

  /// Get offers by freelancer
  static Future<List<OfferModel>> getOffersByFreelancer(String freelancerId) async {
    try {
      // In production, this would query the database
      await Future.delayed(const Duration(milliseconds: 500));

      return _demoOffers.where((offer) => offer.freelancerId == freelancerId).toList();
    } catch (e) {
      throw Exception('Failed to get freelancer offers: $e');
    }
  }

  /// Accept an offer
  static Future<OfferModel> acceptOffer(String offerId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));

      final offerIndex = _demoOffers.indexWhere((offer) => offer.id == offerId);
      if (offerIndex == -1) {
        throw Exception('Offer not found');
      }

      final updatedOffer = _demoOffers[offerIndex].copyWith(status: OfferStatus.accepted, acceptedAt: DateTime.now());

      _demoOffers[offerIndex] = updatedOffer;

      // Reject all other offers for the same request
      final requestId = updatedOffer.requestId;
      for (int i = 0; i < _demoOffers.length; i++) {
        if (_demoOffers[i].requestId == requestId && _demoOffers[i].id != offerId) {
          final rejectedOffer = _demoOffers[i].copyWith(
            status: OfferStatus.rejected,
            rejectedAt: DateTime.now(),
            rejectionReason: 'Another offer was accepted',
          );
          _demoOffers[i] = rejectedOffer;

          // Send rejection notification
          SmartNotificationService.notifyOfferRejected(
            freelancerId: rejectedOffer.freelancerId,
            offerId: rejectedOffer.id,
            serviceTitle: 'Service Request',
            rejectionReason: 'Another offer was accepted',
          );
        }
      }

      // Send acceptance notification
      SmartNotificationService.notifyOfferAccepted(
        freelancerId: updatedOffer.freelancerId,
        offerId: updatedOffer.id,
        serviceTitle: 'Service Request',
        offerAmount: updatedOffer.price,
      );

      return updatedOffer;
    } catch (e) {
      throw Exception('Failed to accept offer: $e');
    }
  }

  /// Reject an offer
  static Future<OfferModel> rejectOffer(String offerId, {String? reason}) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));

      final offerIndex = _demoOffers.indexWhere((offer) => offer.id == offerId);
      if (offerIndex == -1) {
        throw Exception('Offer not found');
      }

      final updatedOffer = _demoOffers[offerIndex].copyWith(
        status: OfferStatus.rejected,
        rejectedAt: DateTime.now(),
        rejectionReason: reason ?? 'Offer was rejected',
      );

      _demoOffers[offerIndex] = updatedOffer;

      // Send rejection notification
      SmartNotificationService.notifyOfferRejected(
        freelancerId: updatedOffer.freelancerId,
        offerId: updatedOffer.id,
        serviceTitle: 'Service Request',
        rejectionReason: reason,
      );

      return updatedOffer;
    } catch (e) {
      throw Exception('Failed to reject offer: $e');
    }
  }

  /// Withdraw an offer
  static Future<OfferModel> withdrawOffer(String offerId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));

      final offerIndex = _demoOffers.indexWhere((offer) => offer.id == offerId);
      if (offerIndex == -1) {
        throw Exception('Offer not found');
      }

      if (_demoOffers[offerIndex].status != OfferStatus.pending) {
        throw Exception('Can only withdraw pending offers');
      }

      final updatedOffer = _demoOffers[offerIndex].copyWith(status: OfferStatus.withdrawn, withdrawnAt: DateTime.now());

      _demoOffers[offerIndex] = updatedOffer;
      return updatedOffer;
    } catch (e) {
      throw Exception('Failed to withdraw offer: $e');
    }
  }

  /// Check if freelancer has already submitted an offer for a request
  static Future<bool> hasFreelancerSubmittedOffer(String freelancerId, String requestId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 200));

      return _demoOffers.any(
        (offer) =>
            offer.freelancerId == freelancerId && offer.requestId == requestId && offer.status != OfferStatus.withdrawn,
      );
    } catch (e) {
      throw Exception('Failed to check offer status: $e');
    }
  }

  /// Get offer by ID
  static Future<OfferModel?> getOfferById(String offerId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 200));

      final offers = _demoOffers.where((offer) => offer.id == offerId);
      return offers.isNotEmpty ? offers.first : null;
    } catch (e) {
      throw Exception('Failed to get offer: $e');
    }
  }

  /// Demo storage for offers
  static final List<OfferModel> _demoOffers = [];

  /// Get demo offers for testing
  static List<OfferModel> getDemoOffers() {
    return List.from(_demoOffers);
  }

  /// Clear demo offers (for testing)
  static void clearDemoOffers() {
    _demoOffers.clear();
  }

  /// Add demo offers for testing
  static void addDemoOffers() {
    _demoOffers.clear();
    _demoOffers.addAll([
      OfferModel(
        id: 'demo_offer_1',
        requestId: 'req_001', // Match with demo order request ID
        freelancerId: 'freelancer_1',
        freelancerName: 'أحمد المطور',
        freelancerAvatar: '👨‍💻',
        freelancerRating: 4.8,
        price: 1500.0,
        description:
            'سأقوم بتطوير تطبيق جوال احترافي باستخدام Flutter مع تصميم حديث وواجهة مستخدم سهلة الاستخدام. سأضمن جودة عالية وتسليم في الوقت المحدد.',
        deliveryDays: 7,
        deliveryTimeUnit: DeliveryTimeUnit.days,
        status: OfferStatus.pending,
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      OfferModel(
        id: 'demo_offer_2',
        requestId: 'req_001', // Same request, multiple offers
        freelancerId: 'freelancer_2',
        freelancerName: 'سارة المصممة',
        freelancerAvatar: '👩‍🎨',
        freelancerRating: 4.6,
        price: 1200.0,
        description:
            'أستطيع تصميم وتطوير تطبيق جوال مميز مع التركيز على تجربة المستخدم والتصميم الجذاب. لدي خبرة واسعة في Flutter وتطوير التطبيقات.',
        deliveryDays: 10,
        deliveryTimeUnit: DeliveryTimeUnit.days,
        status: OfferStatus.pending,
        createdAt: DateTime.now().subtract(const Duration(hours: 4)),
      ),
      OfferModel(
        id: 'demo_offer_3',
        requestId: 'req_0', // Match with another demo order
        freelancerId: 'freelancer_1',
        freelancerName: 'أحمد المطور',
        freelancerAvatar: '👨‍💻',
        freelancerRating: 4.8,
        price: 800.0,
        description:
            'سأقوم بكتابة تقرير علمي شامل ومفصل مع المراجع والمصادر الأكاديمية المطلوبة. لدي خبرة في البحث العلمي والكتابة الأكاديمية.',
        deliveryDays: 5,
        deliveryTimeUnit: DeliveryTimeUnit.days,
        status: OfferStatus.accepted,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        acceptedAt: DateTime.now().subtract(const Duration(hours: 12)),
      ),
      OfferModel(
        id: 'demo_offer_4',
        requestId: 'req_001', // Third offer for same request
        freelancerId: 'freelancer_3',
        freelancerName: 'محمد الخبير',
        freelancerAvatar: '👨‍💼',
        freelancerRating: 4.9,
        price: 1800.0,
        description:
            'خبير في تطوير التطبيقات المتقدمة مع أكثر من 8 سنوات خبرة. سأقدم حلول مبتكرة وتقنيات حديثة لضمان نجاح مشروعك.',
        deliveryDays: 12,
        deliveryTimeUnit: DeliveryTimeUnit.days,
        status: OfferStatus.pending,
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
      ),
    ]);
  }
}
