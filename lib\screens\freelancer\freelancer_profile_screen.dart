import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../providers/language_provider.dart';
import '../../providers/theme_provider.dart';
import '../../models/user_model.dart';
import '../../models/freelancer_earnings_model.dart';
import '../../models/review_model.dart';
import '../../services/payout_service.dart';
import '../../widgets/notifications/notification_widgets.dart';
import '../notifications/notifications_screen.dart';
import 'payout_screen.dart';

class FreelancerProfileScreen extends StatefulWidget {
  final bool showBackButton;

  const FreelancerProfileScreen({super.key, this.showBackButton = false});

  @override
  State<FreelancerProfileScreen> createState() => _FreelancerProfileScreenState();
}

class _FreelancerProfileScreenState extends State<FreelancerProfileScreen> {
  FreelancerEarningsModel? _earnings;
  List<ReviewModel> _reviews = [];
  bool _isLoading = true;
  bool _isUpdatingAvatar = false;

  @override
  void initState() {
    super.initState();
    _loadProfileData();
  }

  Future<void> _loadProfileData() async {
    final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
    final userId = authProvider.user?.id;

    if (userId != null) {
      try {
        final earnings = await PayoutService.getFreelancerEarnings(userId);
        final reviews = await _getDemoReviews(userId);

        if (mounted) {
          setState(() {
            _earnings = earnings;
            _reviews = reviews;
            _isLoading = false;
          });
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  Future<List<ReviewModel>> _getDemoReviews(String freelancerId) async {
    // Demo reviews data
    final now = DateTime.now();
    return [
      ReviewModel(
        id: 'review_1',
        orderId: 'order_1',
        clientId: 'client_1',
        freelancerId: freelancerId,
        clientName: 'Ahmed Mohamed',
        rating: 5,
        reviewText: 'Excellent work! Very professional and delivered on time. Highly recommended!',
        reviewTextAr: 'عمل ممتاز! محترف جداً وسلم في الوقت المحدد. أنصح به بشدة!',
        serviceType: 'Mobile Development',
        createdAt: now.subtract(const Duration(days: 3)),
      ),
      ReviewModel(
        id: 'review_2',
        orderId: 'order_2',
        clientId: 'client_2',
        freelancerId: freelancerId,
        clientName: 'Sara Ali',
        rating: 4,
        reviewText: 'Good quality work, but took a bit longer than expected. Overall satisfied.',
        reviewTextAr: 'عمل جيد الجودة، لكن استغرق وقتاً أطول من المتوقع. راضية بشكل عام.',
        serviceType: 'Web Design',
        createdAt: now.subtract(const Duration(days: 10)),
      ),
      ReviewModel(
        id: 'review_3',
        orderId: 'order_3',
        clientId: 'client_3',
        freelancerId: freelancerId,
        clientName: 'Omar Hassan',
        rating: 5,
        reviewText: 'Amazing developer! Clean code and great communication throughout the project.',
        reviewTextAr: 'مطور رائع! كود نظيف وتواصل ممتاز طوال المشروع.',
        serviceType: 'Flutter Development',
        createdAt: now.subtract(const Duration(days: 15)),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Consumer3<DemoAuthProvider, LanguageProvider, ThemeProvider>(
      builder: (context, authProvider, languageProvider, themeProvider, child) {
        final isArabic = languageProvider.isArabic;
        final isDark = themeProvider.isDarkMode;
        final user = authProvider.userProfile;

        if (user == null) {
          return const Scaffold(body: Center(child: Text('User not found')));
        }

        return Directionality(
          textDirection: languageProvider.textDirection,
          child:
              widget.showBackButton
                  ? Scaffold(
                    backgroundColor: isDark ? ThemeProvider.darkBackground : ThemeProvider.lightBackground,
                    appBar: AppBar(
                      leading: IconButton(icon: const Icon(Icons.arrow_back), onPressed: () => Navigator.pop(context)),
                      title: Text(isArabic ? 'الملف الشخصي' : 'Profile'),
                      backgroundColor: isDark ? ThemeProvider.darkCardBackground : Colors.white,
                      foregroundColor: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                      elevation: 0,
                      actions: [
                        NotificationBell(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(builder: (context) => const NotificationsScreen()),
                            );
                          },
                        ),
                      ],
                    ),
                    body: _buildProfileBody(user, isArabic, isDark),
                  )
                  : Container(
                    color: isDark ? ThemeProvider.darkBackground : ThemeProvider.lightBackground,
                    child: _buildProfileBody(user, isArabic, isDark),
                  ),
        );
      },
    );
  }

  Widget _buildProfileBody(UserModel user, bool isArabic, bool isDark) {
    return _isLoading
        ? const Center(child: CircularProgressIndicator())
        : RefreshIndicator(
          onRefresh: _loadProfileData,
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildProfileHeader(user, isArabic, isDark),
                const SizedBox(height: 24),
                _buildEarningsCard(isArabic, isDark),
                const SizedBox(height: 24),
                _buildSkillsSection(user, isArabic, isDark),
                const SizedBox(height: 24),
                _buildPerformanceMetrics(user, isArabic, isDark),
                const SizedBox(height: 24),
                _buildReviewsSection(isArabic, isDark),
              ],
            ),
          ),
        );
  }

  Widget _buildProfileHeader(UserModel user, bool isArabic, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? ThemeProvider.darkCardBackground : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Profile Picture and Basic Info
          Row(
            children: [
              GestureDetector(
                onTap: () => _showAvatarSelectionDialog(isArabic),
                child: Stack(
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient:
                            user.avatarUrl == null
                                ? LinearGradient(
                                  colors: [
                                    Theme.of(context).colorScheme.primary,
                                    Theme.of(context).colorScheme.primary.withValues(alpha: 0.7),
                                  ],
                                )
                                : null,
                      ),
                      child:
                          user.avatarUrl != null
                              ? ClipOval(
                                child: Image.network(
                                  user.avatarUrl!,
                                  width: 80,
                                  height: 80,
                                  fit: BoxFit.cover,
                                  errorBuilder:
                                      (context, error, stackTrace) => Container(
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          gradient: LinearGradient(
                                            colors: [
                                              Theme.of(context).colorScheme.primary,
                                              Theme.of(context).colorScheme.primary.withValues(alpha: 0.7),
                                            ],
                                          ),
                                        ),
                                        child: const Icon(Icons.person, size: 40, color: Colors.white),
                                      ),
                                ),
                              )
                              : const Icon(Icons.person, size: 40, color: Colors.white),
                    ),
                    if (_isUpdatingAvatar)
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.black.withValues(alpha: 0.5)),
                          child: const Center(
                            child: SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            ),
                          ),
                        ),
                      ),
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Theme.of(context).colorScheme.primary,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                        child: const Icon(Icons.camera_alt, size: 12, color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.fullName ?? (isArabic ? 'مستقل' : 'Freelancer'),
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.star, size: 16, color: Colors.amber[600]),
                        const SizedBox(width: 4),
                        Text(
                          '${user.rating?.toStringAsFixed(1) ?? '0.0'} (${user.completedJobs ?? 0} ${isArabic ? 'مهمة' : 'jobs'})',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    if (user.isVerified) ...[
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(Icons.verified, size: 16, color: Colors.green[600]),
                          const SizedBox(width: 4),
                          Text(
                            isArabic ? 'موثق' : 'Verified',
                            style: Theme.of(
                              context,
                            ).textTheme.bodySmall?.copyWith(color: Colors.green[600], fontWeight: FontWeight.w600),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),

          if (user.bio != null) ...[
            const SizedBox(height: 16),
            Text(
              user.bio!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[700],
                height: 1.4,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEarningsCard(bool isArabic, bool isDark) {
    if (_earnings == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.primary.withValues(alpha: 0.8)],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                isArabic ? 'الأرباح' : 'Earnings',
                style: const TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
              ),
              Icon(Icons.account_balance_wallet, color: Colors.white.withValues(alpha: 0.8)),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            '${_earnings!.availableBalance.toStringAsFixed(2)} ريال',
            style: const TextStyle(color: Colors.white, fontSize: 28, fontWeight: FontWeight.bold),
          ),
          Text(
            isArabic ? 'الرصيد المتاح' : 'Available Balance',
            style: TextStyle(color: Colors.white.withValues(alpha: 0.8), fontSize: 14),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed:
                      _earnings!.canWithdraw
                          ? () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(builder: (context) => PayoutScreen(earnings: _earnings!)),
                            );
                          }
                          : null,
                  icon: const Icon(Icons.payment),
                  label: Text(isArabic ? 'طلب سحب' : 'Request Withdrawal'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Theme.of(context).colorScheme.primary,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
          if (!_earnings!.canWithdraw) ...[
            const SizedBox(height: 8),
            Text(
              '${isArabic ? 'الحد الأدنى للسحب' : 'Minimum withdrawal'}: ${_earnings!.withdrawalThreshold.toStringAsFixed(0)} ريال',
              style: TextStyle(color: Colors.white.withValues(alpha: 0.7), fontSize: 12),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPerformanceMetrics(UserModel user, bool isArabic, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? ThemeProvider.darkCardBackground : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'إحصائيات الأداء' : 'Performance Metrics',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildMetricItem(
                  icon: Icons.work_outline,
                  value: '${user.completedJobs ?? 0}',
                  label: isArabic ? 'مهمة مكتملة' : 'Completed Jobs',
                  color: Colors.blue,
                  isDark: isDark,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMetricItem(
                  icon: Icons.star_outline,
                  value: user.rating?.toStringAsFixed(1) ?? '0.0',
                  label: isArabic ? 'التقييم' : 'Rating',
                  color: Colors.amber,
                  isDark: isDark,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMetricItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
    required bool isDark,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(color: color.withValues(alpha: 0.1), borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
            ),
          ),
          Text(
            label,
            style: TextStyle(fontSize: 12, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSkillsSection(UserModel user, bool isArabic, bool isDark) {
    if (user.skills == null || user.skills!.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? ThemeProvider.darkCardBackground : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            isArabic ? 'المهارات والخدمات' : 'Skills & Services',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children:
                user.skills!.map((skill) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)),
                    ),
                    child: Text(
                      skill,
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  );
                }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewsSection(bool isArabic, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? ThemeProvider.darkCardBackground : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                isArabic ? 'التقييمات والمراجعات' : 'Ratings & Reviews',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                ),
              ),
              Text(
                '${_reviews.length} ${isArabic ? 'تقييم' : 'reviews'}',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (_reviews.isEmpty)
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.rate_review_outlined,
                    size: 48,
                    color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[400],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    isArabic ? 'لا توجد تقييمات بعد' : 'No reviews yet',
                    style: TextStyle(color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                  ),
                ],
              ),
            )
          else
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _reviews.length > 3 ? 3 : _reviews.length,
              separatorBuilder: (context, index) => const Divider(height: 24),
              itemBuilder: (context, index) {
                final review = _reviews[index];
                return _buildReviewItem(review, isArabic, isDark);
              },
            ),
          if (_reviews.length > 3) ...[
            const SizedBox(height: 16),
            Center(
              child: TextButton(
                onPressed: () {
                  // Navigate to full reviews screen
                },
                child: Text(
                  isArabic ? 'عرض جميع التقييمات' : 'View All Reviews',
                  style: TextStyle(color: Theme.of(context).colorScheme.primary, fontWeight: FontWeight.w600),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildReviewItem(ReviewModel review, bool isArabic, bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              ),
              child: Icon(Icons.person, color: Theme.of(context).colorScheme.primary, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    review.clientName,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                    ),
                  ),
                  Row(
                    children: [
                      ...List.generate(5, (index) {
                        return Icon(
                          index < review.rating ? Icons.star : Icons.star_border,
                          size: 16,
                          color: Colors.amber[600],
                        );
                      }),
                      const SizedBox(width: 8),
                      Text(
                        _formatDate(review.createdAt, isArabic),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          isArabic ? (review.reviewTextAr ?? review.reviewText ?? '') : (review.reviewText ?? ''),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[700],
            height: 1.4,
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date, bool isArabic) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return isArabic ? 'اليوم' : 'Today';
    } else if (difference.inDays == 1) {
      return isArabic ? 'أمس' : 'Yesterday';
    } else if (difference.inDays < 7) {
      return isArabic ? '${difference.inDays} أيام' : '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Future<void> _showAvatarSelectionDialog(bool isArabic) async {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * 0.7),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  isArabic ? 'اختر صورة الملف الشخصي' : 'Choose Profile Avatar',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(
                  isArabic ? 'اختر أفاتار يمثلك' : 'Select an avatar that represents you',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                ),
                const SizedBox(height: 20),
                Expanded(
                  child: GridView.builder(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 0.8,
                    ),
                    itemCount: _getAvatarOptions().length,
                    itemBuilder: (context, index) {
                      final avatar = _getAvatarOptions()[index];
                      return _buildAvatarChoice(
                        avatar: avatar,
                        isSelected: false, // You can implement selection state if needed
                        onTap: () => _selectAvatar(avatar['url'], isArabic),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => _removeAvatar(isArabic),
                        child: Text(isArabic ? 'إزالة الصورة' : 'Remove Avatar'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(isArabic ? 'إلغاء' : 'Cancel'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
    );
  }

  List<Map<String, dynamic>> _getAvatarOptions() {
    return [
      {
        'url': 'https://ui-avatars.com/api/?name=John+Smith&background=4285f4&color=fff&size=200&rounded=true',
        'gender': 'male',
      },
      {
        'url': 'https://ui-avatars.com/api/?name=Mike+Johnson&background=34a853&color=fff&size=200&rounded=true',
        'gender': 'male',
      },
      {
        'url': 'https://ui-avatars.com/api/?name=David+Brown&background=ea4335&color=fff&size=200&rounded=true',
        'gender': 'male',
      },
      {
        'url': 'https://ui-avatars.com/api/?name=Sarah+Wilson&background=ff6d01&color=fff&size=200&rounded=true',
        'gender': 'female',
      },
      {
        'url': 'https://ui-avatars.com/api/?name=Emma+Davis&background=9c27b0&color=fff&size=200&rounded=true',
        'gender': 'female',
      },
      {
        'url': 'https://ui-avatars.com/api/?name=Lisa+Miller&background=00bcd4&color=fff&size=200&rounded=true',
        'gender': 'female',
      },
      {
        'url': 'https://ui-avatars.com/api/?name=Robert+Taylor&background=795548&color=fff&size=200&rounded=true',
        'gender': 'male',
      },
      {
        'url': 'https://ui-avatars.com/api/?name=Anna+Garcia&background=607d8b&color=fff&size=200&rounded=true',
        'gender': 'female',
      },
    ];
  }

  Widget _buildAvatarChoice({
    required Map<String, dynamic> avatar,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Theme.of(context).colorScheme.primary : Colors.grey.withValues(alpha: 0.3),
            width: isSelected ? 3 : 1,
          ),
        ),
        child: Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(8), color: Colors.grey.withValues(alpha: 0.1)),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              avatar['url'],
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  color: Colors.grey.withValues(alpha: 0.1),
                  child: const Center(child: CircularProgressIndicator(strokeWidth: 2)),
                );
              },
              errorBuilder:
                  (context, error, stackTrace) => Container(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    child: Icon(Icons.person, size: 40, color: Theme.of(context).colorScheme.primary),
                  ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _selectAvatar(String avatarUrl, bool isArabic) async {
    Navigator.pop(context);

    try {
      setState(() {
        _isUpdatingAvatar = true;
      });

      // Update user profile with selected avatar
      final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
      final success = await authProvider.updateProfile(avatarUrl: avatarUrl);

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(isArabic ? 'تم تحديث الصورة بنجاح' : 'Avatar updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          throw Exception('Failed to update profile');
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'فشل في تحديث الصورة' : 'Failed to update avatar'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdatingAvatar = false;
        });
      }
    }
  }

  Future<void> _removeAvatar(bool isArabic) async {
    Navigator.pop(context);

    try {
      setState(() {
        _isUpdatingAvatar = true;
      });

      // Update user profile to remove avatar
      final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
      final success = await authProvider.updateProfile(avatarUrl: null);

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(isArabic ? 'تم إزالة الصورة بنجاح' : 'Profile picture removed successfully'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          throw Exception('Failed to update profile');
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'فشل في إزالة الصورة' : 'Failed to remove profile picture'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdatingAvatar = false;
        });
      }
    }
  }
}
