import 'package:flutter/material.dart';

import '../../models/order_model.dart';
import '../../providers/theme_provider.dart';
import '../chat/chat_screen.dart';

class OrderDetailView extends StatefulWidget {
  final OrderModel order;
  final bool isArabic;

  const OrderDetailView({super.key, required this.order, required this.isArabic});

  @override
  State<OrderDetailView> createState() => _OrderDetailViewState();
}

class _OrderDetailViewState extends State<OrderDetailView> {
  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Directionality(
      textDirection: widget.isArabic ? TextDirection.rtl : TextDirection.ltr,
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            widget.isArabic ? 'تفاصيل الطلب' : 'Order Details',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          backgroundColor: isDark ? ThemeProvider.darkCardBackground : Colors.white,
          foregroundColor: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
          elevation: 0,
          leading: IconButton(
            icon: Icon(widget.isArabic ? Icons.arrow_forward_ios : Icons.arrow_back_ios, size: 20),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Order Header Card
              _buildOrderHeaderCard(isDark),
              const SizedBox(height: 16),

              // Order Description Card
              _buildDescriptionCard(isDark),
              const SizedBox(height: 16),

              // Payment Info Card (only for non-waiting orders)
              if (!_isWaitingOrder()) ...[_buildPaymentInfoCard(isDark), const SizedBox(height: 16)],

              // Timeline Card
              _buildTimelineCard(isDark),
              const SizedBox(height: 16),

              // Action Buttons
              _buildActionButtons(isDark),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOrderHeaderCard(bool isDark) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    _getOrderTitle(),
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                    ),
                  ),
                ),
                _buildStatusBadge(),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              widget.isArabic ? 'رقم الطلب: ${widget.order.id}' : 'Order ID: ${widget.order.id}',
              style: TextStyle(fontSize: 14, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
            ),
            // Freelancer Info (only show for non-waiting orders)
            if (!_isWaitingOrder()) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  CircleAvatar(
                    radius: 25,
                    backgroundColor: ThemeProvider.primaryBlue.withValues(alpha: 0.1),
                    child: const Icon(Icons.person, color: ThemeProvider.primaryBlue, size: 24),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.isArabic ? 'المستقل المكلف' : 'Assigned Freelancer',
                          style: TextStyle(
                            fontSize: 12,
                            color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          'أحمد محمد', // Demo freelancer name
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Icon(Icons.star, size: 16, color: Colors.amber[600]),
                            const SizedBox(width: 4),
                            Text(
                              '4.8 (156 ${widget.isArabic ? 'تقييم' : 'reviews'})',
                              style: TextStyle(
                                fontSize: 12,
                                color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDescriptionCard(bool isDark) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.isArabic ? 'وصف المشروع' : 'Project Description',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              _getOrderDescription(),
              style: TextStyle(
                fontSize: 14,
                height: 1.5,
                color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[700],
              ),
            ),
            const SizedBox(height: 16),

            // Attached Files Section
            if (_hasAttachedFiles()) ...[
              Text(
                widget.isArabic ? 'الملفات المرفقة' : 'Attached Files',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              _buildAttachedFilesList(isDark),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentInfoCard(bool isDark) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.isArabic ? 'معلومات الدفع' : 'Payment Information',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
              ),
            ),
            const SizedBox(height: 12),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.isArabic ? 'المبلغ الإجمالي:' : 'Total Amount:',
                  style: TextStyle(fontSize: 14, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[700]),
                ),
                Text(
                  '${widget.order.amount.toStringAsFixed(0)} ${widget.isArabic ? 'ريال' : 'SAR'}',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: ThemeProvider.successGreen),
                ),
              ],
            ),
            const SizedBox(height: 8),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.isArabic ? 'حالة الدفع:' : 'Payment Status:',
                  style: TextStyle(fontSize: 14, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[700]),
                ),
                _buildPaymentStatusBadge(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineCard(bool isDark) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.isArabic ? 'سجل التحديثات' : 'Timeline',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            _buildTimelineItems(isDark),
          ],
        ),
      ),
    );
  }

  // Helper Methods
  bool _isWaitingOrder() {
    return widget.order.status == OrderStatus.created || widget.order.status == OrderStatus.paymentPending;
  }

  String _getOrderTitle() {
    final serviceTitles = {
      'order_001': widget.isArabic ? 'تصميم شعار احترافي للشركة' : 'Professional Company Logo Design',
      'order_002': widget.isArabic ? 'تطوير تطبيق جوال بـ Flutter' : 'Flutter Mobile App Development',
      'order_003': widget.isArabic ? 'ترجمة مقال علمي من الإنجليزية للعربية' : 'Scientific Article Translation EN-AR',
      'order_004': widget.isArabic ? 'تحليل البيانات باستخدام SPSS' : 'Data Analysis using SPSS',
      'order_005': widget.isArabic ? 'مراجعة وتدقيق نص أكاديمي' : 'Academic Text Review & Proofreading',
    };
    return serviceTitles[widget.order.id] ?? (widget.isArabic ? 'خدمة عامة' : 'General Service');
  }

  String _getOrderDescription() {
    final descriptions = {
      'order_001':
          widget.isArabic
              ? 'تصميم شعار احترافي ومميز للشركة يعكس هوية العلامة التجارية ويناسب جميع الاستخدامات الرقمية والمطبوعة.'
              : 'Design a professional and distinctive company logo that reflects the brand identity and is suitable for all digital and print uses.',
      'order_002':
          widget.isArabic
              ? 'تطوير تطبيق جوال متكامل باستخدام Flutter مع واجهة مستخدم حديثة وميزات متقدمة.'
              : 'Develop a complete mobile application using Flutter with modern UI and advanced features.',
      'order_003':
          widget.isArabic
              ? 'ترجمة مقال علمي متخصص من اللغة الإنجليزية إلى العربية مع الحفاظ على المصطلحات العلمية الدقيقة.'
              : 'Translate a specialized scientific article from English to Arabic while maintaining accurate scientific terminology.',
    };
    return descriptions[widget.order.id] ??
        (widget.isArabic ? 'وصف المشروع غير متوفر' : 'Project description not available');
  }

  Widget _buildStatusBadge() {
    Color color;
    String text;
    IconData icon;

    switch (widget.order.status) {
      case OrderStatus.created:
      case OrderStatus.paymentPending:
        color = Colors.orange;
        text = widget.isArabic ? 'في الانتظار' : 'Waiting';
        icon = Icons.hourglass_empty;
        break;
      case OrderStatus.paymentConfirmed:
      case OrderStatus.inProgress:
        color = Colors.blue;
        text = widget.isArabic ? 'قيد التنفيذ' : 'In Progress';
        icon = Icons.work_outline;
        break;
      case OrderStatus.submitted:
      case OrderStatus.delivered:
        color = Colors.teal;
        text = widget.isArabic ? 'مُسلَّم' : 'Delivered';
        icon = Icons.check_circle_outline;
        break;
      case OrderStatus.completed:
        color = Colors.green;
        text = widget.isArabic ? 'مكتمل' : 'Completed';
        icon = Icons.verified;
        break;
      case OrderStatus.cancelled:
        color = Colors.red;
        text = widget.isArabic ? 'ملغي' : 'Canceled';
        icon = Icons.cancel_outlined;
        break;
      default:
        color = Colors.grey;
        text = widget.isArabic ? 'غير محدد' : 'Unknown';
        icon = Icons.help_outline;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 6),
          Text(text, style: TextStyle(color: color, fontWeight: FontWeight.bold, fontSize: 12)),
        ],
      ),
    );
  }

  Widget _buildPaymentStatusBadge() {
    Color color;
    String text;

    switch (widget.order.paymentStatus) {
      case PaymentStatus.pending:
        color = Colors.orange;
        text = widget.isArabic ? 'في الانتظار' : 'Pending';
        break;
      case PaymentStatus.confirmed:
        color = Colors.green;
        text = widget.isArabic ? 'مؤكد' : 'Confirmed';
        break;
      case PaymentStatus.rejected:
        color = Colors.red;
        text = widget.isArabic ? 'مرفوض' : 'Rejected';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(text, style: TextStyle(color: color, fontWeight: FontWeight.bold, fontSize: 11)),
    );
  }

  bool _hasAttachedFiles() {
    // Demo logic - in real app, check if order has attached files
    return widget.order.id == 'order_001' || widget.order.id == 'order_002';
  }

  Widget _buildAttachedFilesList(bool isDark) {
    final demoFiles = [
      {'name': 'requirements.pdf', 'size': '2.3 MB', 'type': 'pdf'},
      {'name': 'reference_images.zip', 'size': '5.1 MB', 'type': 'zip'},
    ];

    return Column(
      children:
          demoFiles
              .map(
                (file) => Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isDark ? Colors.grey[800] : Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        file['type'] == 'pdf' ? Icons.picture_as_pdf : Icons.archive,
                        color: file['type'] == 'pdf' ? Colors.red : Colors.blue,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              file['name']!,
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                              ),
                            ),
                            Text(
                              file['size']!,
                              style: TextStyle(
                                fontSize: 12,
                                color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: () {
                          // Download file logic
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(widget.isArabic ? 'تم تحميل الملف' : 'File downloaded'),
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                        icon: const Icon(Icons.download),
                      ),
                    ],
                  ),
                ),
              )
              .toList(),
    );
  }

  Widget _buildActionButtons(bool isDark) {
    return Column(
      children: [
        // Chat Button (only for non-waiting orders)
        if (!_isWaitingOrder()) ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _openChat,
              icon: const Icon(Icons.chat_bubble_outline),
              label: Text(
                widget.isArabic ? 'فتح المحادثة' : 'Open Chat',
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: ThemeProvider.primaryBlue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              ),
            ),
          ),
          const SizedBox(height: 12),
        ],

        // Status-specific action buttons
        ..._buildStatusSpecificButtons(isDark),
      ],
    );
  }

  Widget _buildTimelineItems(bool isDark) {
    final timelineItems = [
      {
        'title': widget.isArabic ? 'تم إنشاء الطلب' : 'Order Created',
        'time': widget.order.createdAt,
        'icon': Icons.add_circle_outline,
        'color': Colors.blue,
      },
      if (widget.order.paymentStatus == PaymentStatus.confirmed)
        {
          'title': widget.isArabic ? 'تم تأكيد الدفع' : 'Payment Confirmed',
          'time': widget.order.createdAt.add(const Duration(hours: 1)),
          'icon': Icons.payment,
          'color': Colors.green,
        },
      if (widget.order.status == OrderStatus.inProgress ||
          widget.order.status == OrderStatus.delivered ||
          widget.order.status == OrderStatus.completed)
        {
          'title': widget.isArabic ? 'بدء العمل' : 'Work Started',
          'time': widget.order.createdAt.add(const Duration(hours: 2)),
          'icon': Icons.work_outline,
          'color': Colors.orange,
        },
      if (widget.order.status == OrderStatus.delivered || widget.order.status == OrderStatus.completed)
        {
          'title': widget.isArabic ? 'تم تسليم العمل' : 'Work Delivered',
          'time': widget.order.deliveryDate ?? widget.order.createdAt.add(const Duration(days: 3)),
          'icon': Icons.check_circle_outline,
          'color': Colors.teal,
        },
      if (widget.order.status == OrderStatus.completed)
        {
          'title': widget.isArabic ? 'تم إكمال الطلب' : 'Order Completed',
          'time': widget.order.updatedAt ?? widget.order.createdAt.add(const Duration(days: 4)),
          'icon': Icons.verified,
          'color': Colors.green,
        },
    ];

    return Column(
      children:
          timelineItems.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            final isLast = index == timelineItems.length - 1;

            return Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  children: [
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: (item['color'] as Color).withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                        border: Border.all(color: item['color'] as Color, width: 2),
                      ),
                      child: Icon(item['icon'] as IconData, size: 16, color: item['color'] as Color),
                    ),
                    if (!isLast) Container(width: 2, height: 40, color: Colors.grey[300]),
                  ],
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(bottom: isLast ? 0 : 24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item['title'] as String,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _formatDateTime(item['time'] as DateTime),
                          style: TextStyle(
                            fontSize: 12,
                            color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          }).toList(),
    );
  }

  List<Widget> _buildStatusSpecificButtons(bool isDark) {
    List<Widget> buttons = [];

    // Debug print to help troubleshoot
    print('Order Status: ${widget.order.status}');
    print('Payment Status: ${widget.order.paymentStatus}');
    print('Order Amount: ${widget.order.amount}');

    switch (widget.order.status) {
      case OrderStatus.created:
      case OrderStatus.paymentPending:
        // Pay Now button removed as requested
        break;

      case OrderStatus.delivered:
        buttons.addAll([
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _confirmDelivery,
              icon: const Icon(Icons.check_circle),
              label: Text(
                widget.isArabic ? 'تأكيد الاستلام' : 'Confirm Delivery',
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              ),
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _requestRevision,
              icon: const Icon(Icons.edit),
              label: Text(
                widget.isArabic ? 'طلب تعديل' : 'Request Revision',
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              ),
            ),
          ),
        ]);
        break;

      case OrderStatus.completed:
        if (widget.order.clientRating == null) {
          buttons.add(
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _rateFreelancer,
                icon: const Icon(Icons.star),
                label: Text(
                  widget.isArabic ? 'تقييم المستقل' : 'Rate Freelancer',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
              ),
            ),
          );
        }
        break;

      default:
        break;
    }

    return buttons;
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _openChat() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => ChatScreen(
              chatId: widget.order.id,
              recipientName: widget.isArabic ? 'المستقل' : 'Freelancer',
              requestTitle: widget.isArabic ? 'طلب رقم ${widget.order.id}' : 'Order #${widget.order.id}',
              isAdminChat: false,
              orderId: widget.order.id,
            ),
      ),
    );
  }

  void _confirmDelivery() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(widget.isArabic ? 'تم تأكيد استلام العمل' : 'Delivery confirmed'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _requestRevision() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(widget.isArabic ? 'تم إرسال طلب التعديل' : 'Revision request sent'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _rateFreelancer() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(widget.isArabic ? 'تم فتح صفحة التقييم' : 'Rating page opened'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
