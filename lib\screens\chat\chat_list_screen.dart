import 'package:flutter/material.dart';
import '../../utils/app_localizations.dart';
import '../../models/chat_model.dart';
import '../../widgets/chat/enhanced_chat_widgets.dart';
import '../../widgets/notifications/notification_widgets.dart';
import '../notifications/notifications_screen.dart';
import 'chat_screen.dart';

class ChatListScreen extends StatefulWidget {
  final bool showBackButton;

  const ChatListScreen({super.key, this.showBackButton = false});

  @override
  State<ChatListScreen> createState() => _ChatListScreenState();
}

class _ChatListScreenState extends State<ChatListScreen> {
  List<Map<String, dynamic>> _getDemoChats(AppLocalizations l10n) {
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';

    return [
      // Customer Service Chat - Always at the top
      {
        'id': 'customer_service_demo',
        'name': isArabic ? 'خدمة العملاء' : 'Customer Service',
        'lastMessage': isArabic ? 'مرحباً! كيف يمكنني مساعدتك اليوم؟' : 'Hello! How can I help you today?',
        'time': isArabic ? 'متاح الآن' : 'Available now',
        'unreadCount': 0,
        'isOnline': true,
        'avatar': '👨‍💼',
        'requestTitle': isArabic ? 'خدمة العملاء' : 'Customer Service',
        'isCustomerService': true,
      },
      // Client-Freelancer Chat
      {
        'id': 'client_freelancer_demo',
        'name': isArabic ? 'أحمد المطور' : 'Ahmed Developer',
        'lastMessage':
            isArabic ? 'سأبدأ العمل على المشروع غداً صباحاً' : 'I will start working on the project tomorrow morning',
        'time': isArabic ? '5 دقائق' : '5 min ago',
        'unreadCount': 1,
        'isOnline': true,
        'avatar': '👨‍💻',
        'requestTitle': isArabic ? 'تطوير تطبيق جوال' : 'Mobile App Development',
        'isCustomerService': false,
      },
    ];
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final demoChats = _getDemoChats(l10n);

    // If showBackButton is true, we're in a standalone screen, so show AppBar
    // If false, we're embedded in main screen, so don't show AppBar
    if (widget.showBackButton) {
      return Scaffold(
        appBar: AppBar(
          leading: IconButton(icon: const Icon(Icons.arrow_back), onPressed: () => Navigator.pop(context)),
          title: Text(l10n.messages),
          actions: [
            NotificationBell(
              onTap: () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => const NotificationsScreen()));
              },
            ),
            IconButton(
              icon: const Icon(Icons.search),
              onPressed: () {
                // Show search functionality
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      Localizations.localeOf(context).languageCode == 'ar'
                          ? 'البحث في المحادثات قريباً'
                          : 'Chat search coming soon',
                    ),
                    duration: const Duration(seconds: 2),
                  ),
                );
              },
            ),
          ],
        ),
        body:
            demoChats.isEmpty
                ? _buildEmptyState(l10n)
                : ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  itemCount: demoChats.length,
                  itemBuilder: (context, index) {
                    final chat = demoChats[index];
                    return _buildChatTile(context, chat, l10n);
                  },
                ),
      );
    }

    // When embedded in main screen, just return the body content
    return demoChats.isEmpty
        ? _buildEmptyState(l10n)
        : ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          itemCount: demoChats.length,
          itemBuilder: (context, index) {
            final chat = demoChats[index];
            return _buildChatTile(context, chat, l10n);
          },
        );
  }

  Widget _buildEmptyState(AppLocalizations l10n) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(l10n.translate('noMessagesYet'), style: Theme.of(context).textTheme.titleLarge),
          const SizedBox(height: 8),
          Text(
            l10n.translate('startConversation'),
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildChatTile(BuildContext context, Map<String, dynamic> chat, AppLocalizations l10n) {
    // Convert demo chat data to ChatModel for enhanced widget
    final chatModel = ChatModel(
      id: chat['id'],
      requestId: 'req_${chat['id']}',
      clientId: 'client1',
      freelancerId: 'freelancer1',
      lastMessage: chat['lastMessage'],
      lastMessageTime: _parseTimeString(chat['time']),
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
    );

    return EnhancedChatListItem(
      chat: chatModel,
      unreadCount: chat['unreadCount'] ?? 0,
      recipientName: chat['name'],
      recipientAvatar: chat['avatar'],
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => ChatScreen(
                  chatId: chat['id'],
                  recipientName: chat['name'],
                  requestTitle: chat['requestTitle'],
                  isAdminChat: chat['isCustomerService'] == true || chat['name'] == 'Admin Support',
                ),
          ),
        );
      },
    );
  }

  DateTime _parseTimeString(String timeStr) {
    // Simple time parsing for demo - in real app this would be proper timestamp
    if (timeStr.contains('min')) {
      final minutes = int.tryParse(timeStr.split(' ')[0]) ?? 0;
      return DateTime.now().subtract(Duration(minutes: minutes));
    } else if (timeStr.contains('hour')) {
      final hours = int.tryParse(timeStr.split(' ')[0]) ?? 0;
      return DateTime.now().subtract(Duration(hours: hours));
    }
    return DateTime.now().subtract(const Duration(hours: 1));
  }
}
