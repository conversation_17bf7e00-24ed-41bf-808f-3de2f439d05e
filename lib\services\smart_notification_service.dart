import 'dart:async';
import 'package:flutter/services.dart';
import '../models/notification_model.dart';
import 'notification_service.dart';

/// Smart Notification Service that handles intelligent notification delivery
/// with real-time push notifications, smart timing, and duplicate prevention
class SmartNotificationService {
  static final Map<String, DateTime> _lastNotificationTime = {};
  static final Map<String, String> _lastNotificationContent = {};
  static const Duration _duplicatePreventionWindow = Duration(minutes: 5);

  /// Initialize the smart notification service
  static Future<void> initialize() async {
    await NotificationService.initialize();
    _startSmartNotificationEngine();
  }

  /// Start the smart notification engine for real-time processing
  static void _startSmartNotificationEngine() {
    // This would integrate with Firebase Cloud Messaging in production
    print('🔔 Smart Notification Engine started');
  }

  /// Core method to create smart notifications with duplicate prevention
  static Future<void> createSmartNotification({
    required String userId,
    required String titleEn,
    required String titleAr,
    required String descriptionEn,
    required String descriptionAr,
    required NotificationType type,
    String? relatedId,
    NotificationPriority priority = NotificationPriority.normal,
    Map<String, dynamic>? metadata,
    String? actionUrl,
    bool bypassDuplicateCheck = false,
  }) async {
    // Generate unique key for duplicate detection
    final duplicateKey = '${userId}_${type}_${relatedId ?? 'general'}';

    // Check for duplicates unless bypassed
    if (!bypassDuplicateCheck && _isDuplicate(duplicateKey, descriptionEn)) {
      print('🚫 Duplicate notification prevented: $duplicateKey');
      return;
    }

    // Create the notification
    final notification = NotificationModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      titleEn: titleEn,
      titleAr: titleAr,
      descriptionEn: descriptionEn,
      descriptionAr: descriptionAr,
      type: type,
      relatedId: relatedId,
      priority: priority,
      createdAt: DateTime.now(),
      metadata: metadata,
      actionUrl: actionUrl,
    );

    // Store notification and trigger delivery
    await NotificationService.createNotification(notification);

    // Update duplicate prevention tracking
    _lastNotificationTime[duplicateKey] = DateTime.now();
    _lastNotificationContent[duplicateKey] = descriptionEn;

    // Trigger haptic feedback for high priority notifications
    if (priority == NotificationPriority.high || priority == NotificationPriority.urgent) {
      HapticFeedback.mediumImpact();
    }

    print('✅ Smart notification created: ${type.name} for user $userId');
  }

  /// Check if notification is a duplicate
  static bool _isDuplicate(String key, String content) {
    final lastTime = _lastNotificationTime[key];
    final lastContent = _lastNotificationContent[key];

    if (lastTime == null) return false;

    final timeDiff = DateTime.now().difference(lastTime);
    return timeDiff < _duplicatePreventionWindow && lastContent == content;
  }

  // ==================== ORDER LIFECYCLE NOTIFICATIONS ====================

  /// Notify when a new offer is received
  static Future<void> notifyOfferReceived({
    required String clientId,
    required String offerId,
    required String freelancerName,
    required double offerAmount,
    required String serviceTitle,
  }) async {
    await createSmartNotification(
      userId: clientId,
      titleEn: 'New Offer Received!',
      titleAr: 'تم استلام عرض جديد!',
      descriptionEn: '$freelancerName sent you an offer of \$${offerAmount.toStringAsFixed(2)} for "$serviceTitle"',
      descriptionAr: 'أرسل لك $freelancerName عرضاً بقيمة \$${offerAmount.toStringAsFixed(2)} لـ "$serviceTitle"',
      type: NotificationType.offerReceived,
      relatedId: offerId,
      priority: NotificationPriority.high,
      metadata: {'freelancer_name': freelancerName, 'offer_amount': offerAmount, 'service_title': serviceTitle},
      actionUrl: '/offers/$offerId',
    );
  }

  /// Notify when an offer is accepted
  static Future<void> notifyOfferAccepted({
    required String freelancerId,
    required String offerId,
    required String serviceTitle,
    required double offerAmount,
  }) async {
    await createSmartNotification(
      userId: freelancerId,
      titleEn: 'Offer Accepted!',
      titleAr: 'تم قبول العرض!',
      descriptionEn: '🎉 Your offer was accepted! Awaiting client payment.',
      descriptionAr: '🎉 تم قبول عرضك! في انتظار دفع العميل.',
      type: NotificationType.offerAccepted,
      relatedId: offerId,
      priority: NotificationPriority.high,
      metadata: {'offer_amount': offerAmount, 'service_title': serviceTitle},
      actionUrl: '/offers/$offerId',
    );
  }

  /// Notify when an offer is rejected
  static Future<void> notifyOfferRejected({
    required String freelancerId,
    required String offerId,
    required String serviceTitle,
    String? rejectionReason,
  }) async {
    await createSmartNotification(
      userId: freelancerId,
      titleEn: 'Offer Not Selected',
      titleAr: 'لم يتم اختيار العرض',
      descriptionEn: '❌ Your offer was not selected for this request.',
      descriptionAr: '❌ لم يتم اختيار عرضك لهذا الطلب.',
      type: NotificationType.offerRejected,
      relatedId: offerId,
      priority: NotificationPriority.normal,
      metadata: {'service_title': serviceTitle, 'rejection_reason': rejectionReason},
      actionUrl: '/offers/$offerId',
    );
  }

  /// Notify when payment is confirmed
  static Future<void> notifyPaymentConfirmed({
    required String clientId,
    required String freelancerId,
    required String orderId,
    required double amount,
  }) async {
    // Notify client
    await createSmartNotification(
      userId: clientId,
      titleEn: 'Payment Confirmed',
      titleAr: 'تم تأكيد الدفع',
      descriptionEn: 'Your payment of \$${amount.toStringAsFixed(2)} has been confirmed. Work will begin shortly.',
      descriptionAr: 'تم تأكيد دفعتك بقيمة \$${amount.toStringAsFixed(2)}. سيبدأ العمل قريباً.',
      type: NotificationType.paymentConfirmed,
      relatedId: orderId,
      priority: NotificationPriority.high,
      metadata: {'amount': amount, 'order_id': orderId},
      actionUrl: '/orders/$orderId',
    );

    // Notify freelancer
    await createSmartNotification(
      userId: freelancerId,
      titleEn: 'Payment Received - Start Work',
      titleAr: 'تم استلام الدفع - ابدأ العمل',
      descriptionEn: 'Payment of \$${amount.toStringAsFixed(2)} confirmed. You can now start working on the project.',
      descriptionAr: 'تم تأكيد الدفع بقيمة \$${amount.toStringAsFixed(2)}. يمكنك الآن البدء في العمل على المشروع.',
      type: NotificationType.paymentConfirmed,
      relatedId: orderId,
      priority: NotificationPriority.high,
      metadata: {'amount': amount, 'order_id': orderId},
      actionUrl: '/orders/$orderId',
    );
  }

  /// Notify when work is delivered
  static Future<void> notifyWorkDelivered({
    required String clientId,
    required String orderId,
    required String freelancerName,
    required String? deliveryNotes,
    List<String>? fileUrls,
  }) async {
    final hasFiles = fileUrls != null && fileUrls.isNotEmpty;
    final filesText = hasFiles ? ' with ${fileUrls.length} file(s)' : '';
    final filesTextAr = hasFiles ? ' مع ${fileUrls.length} ملف' : '';

    await createSmartNotification(
      userId: clientId,
      titleEn: 'Work Delivered!',
      titleAr: 'تم تسليم العمل!',
      descriptionEn: '$freelancerName has delivered your work$filesText. Please review and respond.',
      descriptionAr: 'قام $freelancerName بتسليم عملك$filesTextAr. يرجى المراجعة والرد.',
      type: NotificationType.workDelivered,
      relatedId: orderId,
      priority: NotificationPriority.urgent,
      metadata: {
        'freelancer_name': freelancerName,
        'delivery_notes': deliveryNotes,
        'file_count': fileUrls?.length ?? 0,
        'file_urls': fileUrls,
      },
      actionUrl: '/orders/$orderId/delivery',
    );
  }

  // ==================== REMINDER NOTIFICATIONS ====================

  /// Create reminder for pending payment
  static Future<void> createPaymentReminder({
    required String clientId,
    required String orderId,
    required double amount,
    required int daysPending,
  }) async {
    await createSmartNotification(
      userId: clientId,
      titleEn: 'Payment Reminder',
      titleAr: 'تذكير بالدفع',
      descriptionEn:
          'Your payment of \$${amount.toStringAsFixed(2)} has been pending for $daysPending days. Please complete payment to start work.',
      descriptionAr:
          'دفعتك بقيمة \$${amount.toStringAsFixed(2)} معلقة منذ $daysPending أيام. يرجى إكمال الدفع لبدء العمل.',
      type: NotificationType.reminderPaymentDue,
      relatedId: orderId,
      priority: NotificationPriority.high,
      metadata: {'amount': amount, 'days_pending': daysPending},
      actionUrl: '/orders/$orderId/payment',
    );
  }

  /// Create reminder for pending delivery review
  static Future<void> createDeliveryReviewReminder({
    required String clientId,
    required String orderId,
    required int hoursWaiting,
  }) async {
    await createSmartNotification(
      userId: clientId,
      titleEn: 'Review Delivery',
      titleAr: 'مراجعة التسليم',
      descriptionEn: 'Your work has been delivered $hoursWaiting hours ago. Please review and provide feedback.',
      descriptionAr: 'تم تسليم عملك منذ $hoursWaiting ساعة. يرجى المراجعة وتقديم الملاحظات.',
      type: NotificationType.reminderReviewPending,
      relatedId: orderId,
      priority: NotificationPriority.normal,
      metadata: {'hours_waiting': hoursWaiting},
      actionUrl: '/orders/$orderId/review',
    );
  }

  /// Create reminder for response needed
  static Future<void> createResponseReminder({
    required String userId,
    required String chatId,
    required String senderName,
    required int hoursWaiting,
  }) async {
    await createSmartNotification(
      userId: userId,
      titleEn: 'Response Needed',
      titleAr: 'مطلوب رد',
      descriptionEn: '$senderName is waiting for your response for $hoursWaiting hours.',
      descriptionAr: '$senderName ينتظر ردك منذ $hoursWaiting ساعة.',
      type: NotificationType.reminderResponseNeeded,
      relatedId: chatId,
      priority: NotificationPriority.normal,
      metadata: {'sender_name': senderName, 'hours_waiting': hoursWaiting},
      actionUrl: '/chat/$chatId',
    );
  }

  // ==================== ADMIN NOTIFICATIONS ====================

  /// Send system announcement to all users
  static Future<void> sendSystemAnnouncement({
    required List<String> userIds,
    required String titleEn,
    required String titleAr,
    required String messageEn,
    required String messageAr,
    NotificationPriority priority = NotificationPriority.normal,
  }) async {
    for (final userId in userIds) {
      await createSmartNotification(
        userId: userId,
        titleEn: titleEn,
        titleAr: titleAr,
        descriptionEn: messageEn,
        descriptionAr: messageAr,
        type: NotificationType.systemAnnouncement,
        priority: priority,
        bypassDuplicateCheck: true, // Admin messages should always go through
      );
    }
  }

  /// Send custom admin message to specific user
  static Future<void> sendAdminMessage({
    required String userId,
    required String titleEn,
    required String titleAr,
    required String messageEn,
    required String messageAr,
    NotificationPriority priority = NotificationPriority.high,
    String? actionUrl,
  }) async {
    await createSmartNotification(
      userId: userId,
      titleEn: titleEn,
      titleAr: titleAr,
      descriptionEn: messageEn,
      descriptionAr: messageAr,
      type: NotificationType.adminMessage,
      priority: priority,
      actionUrl: actionUrl,
      bypassDuplicateCheck: true,
    );
  }

  // ==================== UTILITY METHODS ====================

  /// Clear old duplicate prevention data (call periodically)
  static void cleanupDuplicatePreventionData() {
    final cutoff = DateTime.now().subtract(const Duration(hours: 24));
    _lastNotificationTime.removeWhere((key, time) => time.isBefore(cutoff));
    _lastNotificationContent.removeWhere((key, content) => !_lastNotificationTime.containsKey(key));
  }

  /// Get notification statistics
  static Map<String, dynamic> getNotificationStats() {
    return {
      'duplicate_prevention_entries': _lastNotificationTime.length,
      'last_cleanup': DateTime.now().toIso8601String(),
    };
  }
}
