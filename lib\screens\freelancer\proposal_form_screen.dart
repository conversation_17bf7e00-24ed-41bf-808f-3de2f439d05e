import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../models/service_request_model.dart';
import '../../models/offer_model.dart';
import '../../services/offer_service.dart';

class ProposalFormScreen extends StatefulWidget {
  final ServiceRequestModel request;

  const ProposalFormScreen({super.key, required this.request});

  @override
  State<ProposalFormScreen> createState() => _ProposalFormScreenState();
}

class _ProposalFormScreenState extends State<ProposalFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _deliveryTimeController = TextEditingController();

  DeliveryTimeUnit _selectedTimeUnit = DeliveryTimeUnit.days;
  bool _isSubmitting = false;

  @override
  void dispose() {
    _descriptionController.dispose();
    _priceController.dispose();
    _deliveryTimeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isArabic = languageProvider.isArabic;

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: Scaffold(
        appBar: AppBar(
          title: Text(isArabic ? 'إرسال عرض' : 'Send Offer', style: const TextStyle(fontWeight: FontWeight.bold)),
          centerTitle: true,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Request Info Card
                _buildRequestInfoCard(isArabic),
                const SizedBox(height: 24),

                // Proposal Description
                _buildDescriptionField(isArabic),
                const SizedBox(height: 20),

                // Proposed Price
                _buildPriceField(isArabic),
                const SizedBox(height: 20),

                // Delivery Time
                _buildDeliveryTimeField(isArabic),
                const SizedBox(height: 32),

                // Submit Button
                _buildSubmitButton(isArabic),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRequestInfoCard(bool isArabic) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isArabic ? 'تفاصيل الطلب' : 'Request Details',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Text(widget.request.title, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
            const SizedBox(height: 8),
            Text(
              widget.request.description,
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.category, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(widget.request.category ?? 'General', style: TextStyle(fontSize: 12, color: Colors.grey[600])),
                const Spacer(),
                Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  isArabic
                      ? 'منذ ${_getTimeAgo(widget.request.createdAt)}'
                      : '${_getTimeAgo(widget.request.createdAt)} ago',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDescriptionField(bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'وصف العرض *' : 'Proposal Description *',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _descriptionController,
          maxLines: 5,
          decoration: InputDecoration(
            hintText:
                isArabic
                    ? 'اشرح كيف ستنفذ هذا المشروع، بما في ذلك الطرق أو أي شروط محددة...'
                    : 'Explain how you will execute this project, including methods or any specific conditions...',
            border: const OutlineInputBorder(),
            alignLabelWithHint: true,
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return isArabic ? 'يرجى إدخال وصف العرض' : 'Please enter proposal description';
            }
            if (value.trim().length < 50) {
              return isArabic ? 'يجب أن يكون الوصف 50 حرف على الأقل' : 'Description must be at least 50 characters';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildPriceField(bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'السعر المقترح *' : 'Proposed Price *',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _priceController,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            hintText: isArabic ? 'أدخل السعر' : 'Enter price',
            border: const OutlineInputBorder(),
            suffixText: isArabic ? 'ريال' : 'SAR',
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return isArabic ? 'يرجى إدخال السعر' : 'Please enter price';
            }
            final price = double.tryParse(value);
            if (price == null || price <= 0) {
              return isArabic ? 'يرجى إدخال سعر صحيح' : 'Please enter a valid price';
            }
            if (price < 50) {
              return isArabic ? 'الحد الأدنى للسعر 50 ريال' : 'Minimum price is 50 SAR';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildDeliveryTimeField(bool isArabic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          isArabic ? 'وقت التسليم *' : 'Delivery Time *',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              flex: 2,
              child: TextFormField(
                controller: _deliveryTimeController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: isArabic ? 'المدة' : 'Duration',
                  border: const OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return isArabic ? 'يرجى إدخال المدة' : 'Please enter duration';
                  }
                  final time = int.tryParse(value);
                  if (time == null || time <= 0) {
                    return isArabic ? 'يرجى إدخال مدة صحيحة' : 'Please enter valid duration';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              flex: 3,
              child: DropdownButtonFormField<DeliveryTimeUnit>(
                value: _selectedTimeUnit,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                items:
                    DeliveryTimeUnit.values.map((unit) {
                      return DropdownMenuItem(
                        value: unit,
                        child: Text(isArabic ? _getTimeUnitArabic(unit) : _getTimeUnitEnglish(unit)),
                      );
                    }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedTimeUnit = value!;
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSubmitButton(bool isArabic) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isSubmitting ? null : _submitOffer,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child:
            _isSubmitting
                ? const CircularProgressIndicator(color: Colors.white)
                : Text(
                  isArabic ? 'إرسال العرض' : 'Send Offer',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
      ),
    );
  }

  void _submitOffer() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
      final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
      final isArabic = languageProvider.isArabic;

      final offer = OfferModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        requestId: widget.request.id,
        freelancerId: authProvider.user!.id,
        freelancerName: authProvider.userProfile?.fullName ?? 'Freelancer',
        freelancerAvatar: authProvider.userProfile?.avatarUrl ?? '👨‍💻',
        freelancerRating: authProvider.userProfile?.rating ?? 4.5,
        price: double.parse(_priceController.text),
        description: _descriptionController.text.trim(),
        deliveryDays: int.parse(_deliveryTimeController.text),
        deliveryTimeUnit: _selectedTimeUnit,
        createdAt: DateTime.now(),
      );

      await OfferService.submitOffer(offer);

      if (mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isArabic
                  ? '✅ تم إرسال عرضك بنجاح. في انتظار رد العميل.'
                  : '✅ Your offer has been submitted. Waiting for client\'s response.',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );

        // Navigate back
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              Provider.of<LanguageProvider>(context, listen: false).isArabic
                  ? 'حدث خطأ أثناء إرسال العرض'
                  : 'Error submitting offer',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else {
      return '${difference.inMinutes}m';
    }
  }

  String _getTimeUnitArabic(DeliveryTimeUnit unit) {
    switch (unit) {
      case DeliveryTimeUnit.hours:
        return 'ساعة';
      case DeliveryTimeUnit.days:
        return 'يوم';
      case DeliveryTimeUnit.weeks:
        return 'أسبوع';
    }
  }

  String _getTimeUnitEnglish(DeliveryTimeUnit unit) {
    switch (unit) {
      case DeliveryTimeUnit.hours:
        return 'Hours';
      case DeliveryTimeUnit.days:
        return 'Days';
      case DeliveryTimeUnit.weeks:
        return 'Weeks';
    }
  }
}
