import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../providers/language_provider.dart';
import '../../models/service_request_model.dart';

import 'create_request_screen.dart';

class MyRequestsScreen extends StatefulWidget {
  const MyRequestsScreen({super.key});

  @override
  State<MyRequestsScreen> createState() => _MyRequestsScreenState();
}

class _MyRequestsScreenState extends State<MyRequestsScreen> {
  List<ServiceRequestModel> _requests = [];
  bool _isLoading = true;
  RequestStatus? _filterStatus;

  @override
  void initState() {
    super.initState();
    _loadRequests();
  }

  Future<void> _loadRequests() async {
    final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
    final userId = authProvider.user?.id;

    if (userId != null) {
      try {
        // For demo purposes, create some sample requests
        await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay

        final allDemoRequests = _createDemoRequests(userId);

        // Filter by status if needed
        final filteredRequests =
            _filterStatus != null
                ? allDemoRequests.where((request) => request.status == _filterStatus).toList()
                : allDemoRequests;

        setState(() {
          _requests = filteredRequests;
          _isLoading = false;
        });
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
      }
    } else {
      // If no user is logged in, just show empty state
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<ServiceRequestModel> _createDemoRequests(String userId) {
    return [
      ServiceRequestModel(
        id: '1',
        clientId: userId,
        title: 'تصميم موقع إلكتروني للشركة',
        description: 'أحتاج إلى تصميم موقع إلكتروني احترافي لشركتي مع نظام إدارة المحتوى',
        category: 'تصميم وبرمجة المواقع',
        budget: 1500.0,
        deadline: DateTime.now().add(const Duration(days: 30)),
        status: RequestStatus.pending,
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      ServiceRequestModel(
        id: '2',
        clientId: userId,
        title: 'ترجمة مقال علمي',
        description: 'ترجمة مقال علمي من الإنجليزية إلى العربية في مجال الطب',
        category: 'الترجمة',
        budget: 200.0,
        deadline: DateTime.now().add(const Duration(days: 7)),
        status: RequestStatus.inProgress,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 5)),
      ),
      ServiceRequestModel(
        id: '3',
        clientId: userId,
        title: 'تحليل إحصائي باستخدام SPSS',
        description: 'تحليل البيانات الإحصائية لرسالة الماجستير باستخدام برنامج SPSS',
        category: 'التحليل الإحصائي',
        budget: 300.0,
        deadline: DateTime.now().add(const Duration(days: 14)),
        status: RequestStatus.completed,
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isArabic = languageProvider.isArabic;

    return Scaffold(
      appBar: AppBar(
        title: Text(isArabic ? 'طلباتي' : 'My Requests'),
        actions: [
          PopupMenuButton<RequestStatus?>(
            icon: const Icon(Icons.filter_list),
            onSelected: (status) {
              setState(() {
                _filterStatus = status;
                _isLoading = true;
              });
              _loadRequests();
            },
            itemBuilder:
                (context) => [
                  PopupMenuItem(value: null, child: Text(isArabic ? 'جميع الطلبات' : 'All Requests')),
                  PopupMenuItem(value: RequestStatus.pending, child: Text(isArabic ? 'قيد الانتظار' : 'Pending')),
                  PopupMenuItem(value: RequestStatus.inProgress, child: Text(isArabic ? 'قيد التنفيذ' : 'In Progress')),
                  PopupMenuItem(value: RequestStatus.completed, child: Text(isArabic ? 'مكتمل' : 'Completed')),
                  PopupMenuItem(value: RequestStatus.cancelled, child: Text(isArabic ? 'ملغي' : 'Cancelled')),
                ],
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                onRefresh: () async {
                  setState(() {
                    _isLoading = true;
                  });
                  await _loadRequests();
                },
                child:
                    _requests.isEmpty
                        ? _buildEmptyState(isArabic)
                        : ListView.builder(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          itemCount: _requests.length,
                          itemBuilder: (context, index) {
                            final request = _requests[index];
                            return _buildRequestCard(request, isArabic);
                          },
                        ),
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const CreateRequestScreen()),
          ).then((_) => _loadRequests());
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState(bool isArabic) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.assignment_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            _filterStatus != null
                ? (isArabic
                    ? 'لا توجد طلبات ${_getStatusInArabic(_filterStatus!)}'
                    : 'No ${_filterStatus.toString().split('.').last} requests')
                : (isArabic ? 'لا توجد طلبات بعد' : 'No requests yet'),
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            _filterStatus != null
                ? (isArabic ? 'جرب تغيير الفلتر أو إنشاء طلب جديد' : 'Try changing the filter or create a new request')
                : (isArabic ? 'أنشئ أول طلب خدمة للبدء' : 'Create your first service request to get started'),
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7)),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const CreateRequestScreen()),
              ).then((_) => _loadRequests());
            },
            icon: const Icon(Icons.add),
            label: Text(isArabic ? 'إنشاء طلب' : 'Create Request'),
          ),
        ],
      ),
    );
  }

  Widget _buildRequestCard(ServiceRequestModel request, bool isArabic) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    request.title,
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(request.status).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    isArabic
                        ? _getStatusInArabic(request.status)
                        : request.status.toString().split('.').last.toUpperCase(),
                    style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600, color: _getStatusColor(request.status)),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            if (request.category != null) ...[
              Row(
                children: [
                  Icon(
                    Icons.category_outlined,
                    size: 16,
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    request.category!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],

            Text(
              request.description,
              style: Theme.of(context).textTheme.bodyMedium,
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 12),

            Row(
              children: [
                if (request.budget != null) ...[
                  const Icon(Icons.attach_money, size: 16, color: Colors.green),
                  Text(
                    '\$${request.budget!.toStringAsFixed(0)}',
                    style: const TextStyle(fontWeight: FontWeight.w600, color: Colors.green),
                  ),
                  const SizedBox(width: 16),
                ],
                Icon(Icons.schedule, size: 16, color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6)),
                const SizedBox(width: 4),
                Text(
                  isArabic
                      ? 'الموعد النهائي: ${request.deadline.day}/${request.deadline.month}/${request.deadline.year}'
                      : 'Deadline: ${request.deadline.day}/${request.deadline.month}/${request.deadline.year}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            Row(
              children: [
                Text(
                  isArabic
                      ? 'تم النشر ${_getTimeAgoInArabic(request.createdAt)}'
                      : 'Posted ${_getTimeAgo(request.createdAt)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to request details
                  },
                  child: Text(isArabic ? 'عرض التفاصيل' : 'View Details'),
                ),
                if (request.status == RequestStatus.pending) ...[
                  const SizedBox(width: 8),
                  TextButton(
                    onPressed: () {
                      // Navigate to edit request
                    },
                    child: Text(isArabic ? 'تعديل' : 'Edit'),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(RequestStatus status) {
    switch (status) {
      case RequestStatus.pending:
        return Colors.orange;
      case RequestStatus.inProgress:
        return Colors.blue;
      case RequestStatus.completed:
        return Colors.green;
      case RequestStatus.cancelled:
        return Colors.red;
    }
  }

  String _getStatusInArabic(RequestStatus status) {
    switch (status) {
      case RequestStatus.pending:
        return 'قيد الانتظار';
      case RequestStatus.inProgress:
        return 'قيد التنفيذ';
      case RequestStatus.completed:
        return 'مكتملة';
      case RequestStatus.cancelled:
        return 'ملغية';
    }
  }

  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    }
  }

  String _getTimeAgoInArabic(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays > 1 ? 'أيام' : 'يوم'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours > 1 ? 'ساعات' : 'ساعة'}';
    } else {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes > 1 ? 'دقائق' : 'دقيقة'}';
    }
  }
}
