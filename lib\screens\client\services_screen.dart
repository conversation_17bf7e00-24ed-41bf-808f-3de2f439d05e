import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import 'create_request_screen.dart';

class ServicesScreen extends StatefulWidget {
  const ServicesScreen({super.key});

  @override
  State<ServicesScreen> createState() => _ServicesScreenState();
}

class _ServicesScreenState extends State<ServicesScreen> {
  String _searchQuery = '';

  final List<ServiceItem> _services = [
    ServiceItem(
      id: 'academic_sources',
      titleEn: 'Academic Sources and References',
      titleAr: 'توفير المصادر والمراجع الأكاديمية',
      descriptionEn: 'Comprehensive academic research and citation services',
      descriptionAr: 'خدمات البحث الأكاديمي الشاملة والاستشهادات',
      category: 'academic',
      icon: Icons.library_books,
      color: Colors.blue,
      imageAsset: 'assets/images/academic_sources.png',
    ),
    ServiceItem(
      id: 'scientific_reports',
      titleEn: 'Scientific Reports',
      titleAr: 'التقارير العلمية',
      descriptionEn: 'Professional scientific report writing and analysis',
      descriptionAr: 'كتابة وتحليل التقارير العلمية المهنية',
      category: 'academic',
      icon: Icons.science,
      color: Colors.green,
      imageAsset: 'assets/images/scientific_reports.png',
    ),
    ServiceItem(
      id: 'mind_maps',
      titleEn: 'Mind Maps',
      titleAr: 'الخرائط الذهنية',
      descriptionEn: 'Creative mind mapping and visual organization',
      descriptionAr: 'رسم الخرائط الذهنية والتنظيم البصري الإبداعي',
      category: 'design',
      icon: Icons.account_tree,
      color: Colors.purple,
      imageAsset: 'assets/images/mind_maps.png',
    ),
    ServiceItem(
      id: 'translation',
      titleEn: 'Languages and Translation',
      titleAr: 'اللغات والترجمة',
      descriptionEn: 'Professional translation and language services',
      descriptionAr: 'خدمات الترجمة واللغات المهنية',
      category: 'language',
      icon: Icons.translate,
      color: Colors.orange,
      imageAsset: 'assets/images/translation.png',
    ),
    ServiceItem(
      id: 'summarization',
      titleEn: 'Summarizing Books, Articles, and Lectures',
      titleAr: 'تلخيص كتب - مقالات - محاضرات',
      descriptionEn: 'Comprehensive summarization of academic content',
      descriptionAr: 'تلخيص شامل للمحتوى الأكاديمي',
      category: 'academic',
      icon: Icons.summarize,
      color: Colors.teal,
      imageAsset: 'assets/images/summarization.png',
    ),
    ServiceItem(
      id: 'scientific_projects',
      titleEn: 'Scientific Projects',
      titleAr: 'مشاريع علمية',
      descriptionEn: 'Complete scientific project development and research',
      descriptionAr: 'تطوير المشاريع العلمية والبحثية الكاملة',
      category: 'academic',
      icon: Icons.biotech,
      color: Colors.indigo,
      imageAsset: 'assets/images/scientific_projects.png',
    ),
    ServiceItem(
      id: 'presentations',
      titleEn: 'Presentations (PowerPoint)',
      titleAr: 'عروض تقديمية (PowerPoint)',
      descriptionEn: 'Professional presentation design and content creation',
      descriptionAr: 'تصميم العروض التقديمية المهنية وإنشاء المحتوى',
      category: 'design',
      icon: Icons.slideshow,
      color: Colors.red,
      imageAsset: 'assets/images/presentations.png',
    ),
    ServiceItem(
      id: 'spss_analysis',
      titleEn: 'Statistical Analysis (SPSS)',
      titleAr: 'تحليل إحصائي SPSS',
      descriptionEn: 'Advanced statistical analysis using SPSS software',
      descriptionAr: 'التحليل الإحصائي المتقدم باستخدام برنامج SPSS',
      category: 'technical',
      icon: Icons.analytics,
      color: Colors.cyan,
      imageAsset: 'assets/images/spss_analysis.png',
    ),
    ServiceItem(
      id: 'proofreading',
      titleEn: 'Proofreading and Language Editing',
      titleAr: 'تدقيق لغوي',
      descriptionEn: 'Professional proofreading and language correction',
      descriptionAr: 'التدقيق اللغوي المهني وتصحيح اللغة',
      category: 'language',
      icon: Icons.spellcheck,
      color: Colors.brown,
      imageAsset: 'assets/images/proofreading.png',
    ),
    ServiceItem(
      id: 'programming',
      titleEn: 'Programming and Web Design',
      titleAr: 'البرمجة وتصميم المواقع',
      descriptionEn: 'Full-stack development and modern web design',
      descriptionAr: 'التطوير الشامل وتصميم المواقع الحديثة',
      category: 'technical',
      icon: Icons.code,
      color: Colors.deepPurple,
      imageAsset: 'assets/images/programming.png',
    ),
    ServiceItem(
      id: 'tutorials',
      titleEn: 'Tutorials and Private Lessons',
      titleAr: 'الشرح والدورات',
      descriptionEn: 'Personalized tutoring and educational courses',
      descriptionAr: 'دروس خصوصية ودورات تعليمية مخصصة',
      category: 'academic',
      icon: Icons.school,
      color: Colors.amber,
      imageAsset: 'assets/images/tutorials.png',
    ),
  ];

  List<ServiceItem> get _filteredServices {
    if (_searchQuery.isEmpty) {
      return _services;
    }
    return _services.where((service) {
      return service.titleEn.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          service.titleAr.contains(_searchQuery) ||
          service.descriptionEn.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          service.descriptionAr.contains(_searchQuery);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final isArabic = languageProvider.isArabic;

    return Directionality(
      textDirection: languageProvider.textDirection,
      child: CustomScrollView(
        slivers: [
          // Search Bar Section
          SliverToBoxAdapter(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                boxShadow: [
                  BoxShadow(color: Colors.black.withValues(alpha: 0.05), blurRadius: 10, offset: const Offset(0, 2)),
                ],
              ),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(color: Colors.black.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2)),
                  ],
                ),
                child: TextField(
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                  decoration: InputDecoration(
                    hintText: isArabic ? 'ابحث عن خدمة...' : 'Search for a service...',
                    hintStyle: TextStyle(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                      fontWeight: FontWeight.w600,
                    ),
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon:
                        _searchQuery.isNotEmpty
                            ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                setState(() {
                                  _searchQuery = '';
                                });
                              },
                            )
                            : null,
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(16), borderSide: BorderSide.none),
                    filled: true,
                    fillColor: Colors.white,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                  ),
                ),
              ),
            ),
          ),

          // Services Grid
          SliverPadding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            sliver: SliverGrid(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.95,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
              ),
              delegate: SliverChildBuilderDelegate((context, index) {
                final service = _filteredServices[index];
                return _buildServiceCard(service, isArabic);
              }, childCount: _filteredServices.length),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildServiceCard(ServiceItem service, bool isArabic) {
    return Container(
      key: Key('service_card_${service.id}'), // مفتاح للتحديد في Inspector
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        color: Colors.white,
        boxShadow: [
          BoxShadow(color: Colors.black.withValues(alpha: 0.08), blurRadius: 20, offset: const Offset(0, 4)),
          BoxShadow(color: service.color.withValues(alpha: 0.1), blurRadius: 40, offset: const Offset(0, 8)),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _navigateToCreateRequest(service),
          borderRadius: BorderRadius.circular(20),
          child: Column(
            children: [
              // Header with gradient background and icon
              Container(
                height: 80,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [service.color, service.color.withValues(alpha: 0.8)],
                  ),
                ),
                child: Stack(
                  children: [
                    // Background pattern
                    Positioned(
                      top: -20,
                      right: -20,
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.white.withValues(alpha: 0.1)),
                      ),
                    ),
                    Positioned(
                      bottom: -30,
                      left: -30,
                      child: Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.white.withValues(alpha: 0.05)),
                      ),
                    ),
                    // Main icon
                    Center(
                      child: Container(
                        width: 42,
                        height: 42,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Icon(service.icon, size: 24, color: service.color),
                      ),
                    ),
                  ],
                ),
              ),

              // Content section
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(10),
                  child: Column(
                    children: [
                      // Service Title
                      Text(
                        isArabic ? service.titleAr : service.titleEn,
                        style: const TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w700,
                          color: Colors.black87,
                          height: 1.1,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),

                      // Service Description
                      Flexible(
                        child: Text(
                          isArabic ? service.descriptionAr : service.descriptionEn,
                          style: TextStyle(
                            fontSize: 9,
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.65),
                            height: 1.2,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),

                      const SizedBox(height: 6),

                      // Action button
                      Container(
                        width: double.infinity,
                        height: 32,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(colors: [service.color, service.color.withValues(alpha: 0.8)]),
                          borderRadius: BorderRadius.circular(18),
                          boxShadow: [
                            BoxShadow(
                              color: service.color.withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () => _navigateToCreateRequest(service),
                            borderRadius: BorderRadius.circular(18),
                            child: Center(
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    isArabic ? 'اطلب الآن' : 'Order Now',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 11,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const SizedBox(width: 3),
                                  Icon(
                                    isArabic ? Icons.arrow_back : Icons.arrow_forward,
                                    color: Colors.white,
                                    size: 14,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToCreateRequest(ServiceItem service) {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.isArabic;

    // Show selected service info
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(isArabic ? 'تم اختيار: ${service.titleAr}' : 'Selected: ${service.titleEn}'),
        duration: const Duration(seconds: 1),
      ),
    );

    // Navigate to create request screen with selected service
    Navigator.push(context, MaterialPageRoute(builder: (context) => CreateRequestScreen(selectedService: service)));
  }
}

// Data Models
class ServiceCategory {
  final String id;
  final String nameEn;
  final String nameAr;
  final IconData icon;

  ServiceCategory({required this.id, required this.nameEn, required this.nameAr, required this.icon});
}

class ServiceItem {
  final String id;
  final String titleEn;
  final String titleAr;
  final String descriptionEn;
  final String descriptionAr;
  final String category;
  final IconData icon;
  final Color color;
  final String imageAsset;

  ServiceItem({
    required this.id,
    required this.titleEn,
    required this.titleAr,
    required this.descriptionEn,
    required this.descriptionAr,
    required this.category,
    required this.icon,
    required this.color,
    required this.imageAsset,
  });
}
